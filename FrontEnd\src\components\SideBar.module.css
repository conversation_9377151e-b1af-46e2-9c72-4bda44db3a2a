.windowingArea div {
    display: flex;
    gap: 10px;
}

.examsArea {
    height: 300px;
    border: 3px solid hsl(0, 0%, 50%);
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    white-space: nowrap;
}

.aerationOptions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.aerationOption {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btnRelatorio {
    background-color: white;
    /* Cor inicial */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    /* Sombra */
    transition: background-color 0.3s ease;
    /* Transição suave */
    border: none;
    /* Sem borda */
    padding: 5px;
    /* Pequeno espaçamento */
    cursor: pointer;
    /* Mostra que é clicável */
    display: flex;
    /* Alinha o conteúdo */
    justify-content: center;
    /* Centraliza o ícone */
    align-items: center;
    /* Centraliza verticalmente */
}

.btnRelatorio:hover {
    background-color: #218838;
    /* Cor verde ao passar o mouse */
}

.colorBox {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.customAeration {
    flex-direction: column;
    align-items: flex-start;
}

.customInputs {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 5px;
}

.customInput {
    width: 80px;
    padding: 5px;
}

.customColorInput {
    width: 40px;
    height: 40px;
    padding: 0;
    border: none;
    background: none;
}

.huControls {
    display: flex;
    gap: 5px;
    margin-top: 5px;
}

.huControls input[type="number"] {
    width: 70px;
    padding: 3px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.serieDiv {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.serieName {
    text-overflow: ellipsis;
    max-width: 90%;
    font-weight: 500;
    text-align: left;
    overflow-x: hidden;
}

.serieDeleteButton {
    max-width: 10%;
    font-size: 0.8rem;
    cursor: pointer;
}

.examsArea h3 {
    font-weight: 600;
    margin: 0;
}

.customAeration {
    display: flex;
    align-items: center;
    gap: 10px;
}


.customInputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-left: 10px;
}

.huGroup {
    display: flex;
    gap: 10px;
    align-items: center;
}


.customInput {
    width: 80px;
}

.customColorInput {
    width: 40px;
    height: 40px;
    border: none;
    background: none;
}

.colorBox {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #000;
}

.huSelect {
    width: 150px;
    padding: 5px;
}




.sendDicomsCtm {
    display: none;
}

.sendDicoms {
    color: white;
    padding: 0 0 0 5px;
}

.sendDicomsLabel {
    background-color: hsl(133, 100%, 30%);
    padding: 6px;
    border-radius: 0px 10px 10px 0px;
    color: white;
    overflow: hidden;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.sendDicomsLabel:hover {
    background-color: hsl(133, 100%, 25%);
    transition: background-color 0.4s ease;
}

.sendDicomsDiv {
    font-size: 1.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    border-radius: 10px 10px 0px 0px;
    background-color: hsl(0, 0%, 50%);
}

#LisaTextCtm {
    font-weight: 700;
}

.logoutButton {
    cursor: pointer;
}

.algorithmArea {
    border: 2px solid hsl(0, 0%, 50%);
}

.algorithmArea label {
    font-weight: 700;
}

.algorithmArea div {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.algorithmArea select {
    padding: 6px;
    width: 80%;
}

.algorithmArea button {
    width: 80%;
    display: block;
    background-color: hsl(133, 100%, 30%);
    padding: 6px;
    border-color: hsl(133, 100%, 30%);
    border-radius: 0px 0px 10px 10px;
    color: white;
}

.windowingArea div {
    display: flex;
    gap: 10px;
}

.examLocal {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    padding-left: 5%;
    align-items: center;
    border: 2px solid hsl(0, 0%, 50%);
    border-width: 2px 0px 2px 0px;
    font-size: 15px;
    cursor: default;
}

.examLocal p {
    margin: 0;
    overflow-x: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.examLocal span {
    border-left: 2px solid hsl(0, 0%, 50%);
    cursor: pointer;
}

.spinnerSidebar {
    position: absolute;
    top: 50%;
    /* Move o elemento 50% para baixo em relação ao ancestral */
    left: 50%;
    /* Move o elemento 50% para a direita em relação ao ancestral */
    transform: translate(-50%, -50%);
    /* Ajusta a posição para centralizar o elemento */
    z-index: 1000;
}

.aerationCard {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    background-color: #f9f9f9;
}

.aerationOptions {
    display: flex;
    flex-direction: column;
}

.aerationOption {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

label {
    flex: 1;
    /* Faz o label ocupar o espaço entre o checkbox e o quadrado */
    margin-left: 10px;
    /* Espaçamento entre o checkbox e o texto */
}

.colorBox {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    /* Espaçamento entre o nome e o quadrado */
    border: 1px solid #000;
    /* Borda opcional para os quadrados */
}


/* Estiliza a barra de rolagem para navegadores baseados no WebKit*/
.examsArea::-webkit-scrollbar {
    width: 10px;
}

.examsArea::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 5px;
}

.examsArea::-webkit-scrollbar-thumb {
    background: hsl(133, 100%, 30%);
    /* Cor verde */
    border-radius: 5px;
    transition: background 0.3s ease;
}

.examsArea::-webkit-scrollbar-thumb:hover {
    background: #218838;
}

/* Para navegadores que suportam scrollbar-color (Firefox) */
.examsArea {
    scrollbar-color: hsl(133, 100%, 30%) #f0f0f0;
    scrollbar-width: thin;
}

.janelamentoDiv{
    padding: 20px 5px 20px 5px;
}

.janelamentoDiv div{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 5px;
}
.janelamentoDiv button{
    padding: 5px;
    border-radius: 5px;
}