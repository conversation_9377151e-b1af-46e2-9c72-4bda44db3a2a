import cornerstoneMath from 'cornerstone-math';
import cornerstone from 'cornerstone-core';
import cornerstoneTools from 'cornerstone-tools';
import Hammer from 'hammerjs';

export const configurarCornerstone = (idTela) => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.enable(element);

    // Configuração do cornerstoneTools
    cornerstoneTools.external.cornerstone = cornerstone;
    cornerstoneTools.external.Hammer = Hammer;
    cornerstoneTools.external.cornerstoneMath = cornerstoneMath;

    // Inicializa os cornerstoneTools
    cornerstoneTools.init();

    // Adiciona e habilita a ferramenta de rolagem de stack
    const StackScrollMouseWheelTool = cornerstoneTools.StackScrollMouseWheelTool;
    cornerstoneTools.addTool(StackScrollMouseWheelTool);
    cornerstoneTools.setToolActive('StackScrollMouseWheel', {})

    //novas funcionalidades
    cornerstoneTools.toolColors.setToolColor('green');
    cornerstoneTools.toolColors.setActiveColor('green');
    const ArrowAnnotateTool = cornerstoneTools.ArrowAnnotateTool;
    cornerstoneTools.addTool(ArrowAnnotateTool)
    // Add our tool, and set it's mode
    const EraserTool = cornerstoneTools.EraserTool;
    cornerstoneTools.addTool(EraserTool)
    const LengthTool = cornerstoneTools.LengthTool;
    const FreehandRoiTool = cornerstoneTools.FreehandRoiTool;
    cornerstoneTools.addTool(LengthTool)
    // Adicione a ferramenta de zoom
    cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
    cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
    cornerstoneTools.setToolActive("Wwwc", { mouseButtonMask: 1 });

    // Configure a ferramenta de zoom para ser ativada com o botão direito do mouse
    cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 2 });

    cornerstone.enable(element);
    element.addEventListener('contextmenu', function (event) {
      // Previne o menu padrão do navegador de aparecer
      event.preventDefault();
    });

    // Pan: Middle Click
    cornerstoneTools.addTool(cornerstoneTools.PanTool);
    cornerstoneTools.setToolActive("Pan", { mouseButtonMask: 4 });
    cornerstoneTools.addTool(FreehandRoiTool)
    const ZoomTool = cornerstoneTools.ZoomTool;
    cornerstoneTools.addTool(ZoomTool, {
      configuration: {
        invert: false,
        preventZoomOutsideImage: true,
        minScale: 0.1,
        maxScale: 20.0,
      },
    });
}