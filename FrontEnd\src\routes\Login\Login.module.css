.login {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1; 
    background: #EBE5E0;
}

.loginForms{
    background-color: white;
    width:450px;
    min-width: 400px;
    border: 2px solid #13D8A3;
    border-radius: 20px;
}

#buttonCtm{
    background-color: #13D8A3;
    color:white;
    width: 100%;
}

.checkLabel{
    display: flex;
    justify-content: space-between;
    font-size:14px;
}

.checkLabel span{
    cursor: pointer;
}

.checkLabel a{
    text-decoration: underline !important;
    text-underline-offset: 3px;
}

#formGroupCtm:focus {
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}
#formGroupCtmPas:focus{
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}
#formGroupCtmChe:focus{
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

#formGroupCtmChe:checked {
    border:0;
    background-color: #13D8A3;;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

@media (max-width:600px) {
    .login-forms{
        min-width: 300px;
    }
}