import rota from "../api/rota";

// receber as slices sendo arrastadas e tratar-las
export const handleDrop = (e, setJaSegmentada, setPacientInfos, setArquivos) => {
    e.preventDefault();
    e.stopPropagation();
    setJaSegmentada(false);
    // recebe uma string que representa o endereço dos dicoms
    const imageUrl = e.dataTransfer.getData('text/plain');
    const imageArray = JSON.parse(imageUrl);
    console.log(imageArray)
    const files = imageArray.scans.map(image => rota + image.file);
    console.log(imageUrl)
    const arraySerieMember = {
      serieDescription: imageArray.serieDescription,
      patientName: imageArray.patientName,
      patientAge: imageArray.patientAge,
      patientSex: imageArray.patientSex,
      patientId: imageArray.patientId,
      serieData: imageArray.serieData,
      serieManufatura: imageArray.serieManufatura,
      seriesID: imageArray.seriesID
    };
    setPacientInfos(arraySerieMember);

    setArquivos(files);
}