//bootstrap and css
import styles from './Register.module.css';
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
//react
import { useState, useRef, useEffect } from 'react';
//axios
import axios from '../../api/axios';
//context
import { AuthContext } from '../../context/AuthProvider';
//icons
import { FaInfoCircle } from "react-icons/fa";
import { FcCheckmark } from "react-icons/fc";
import { HiXMark } from "react-icons/hi2";
//regex for validation
const USER_REGEX = /^[A-z][A-z0-9-_@.]{3,23}$/;
const PWD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%-+]).{8,24}$/;

function Register({ setTela }) {
    const registerUrl = '/api/v1/authentication/register/';
    const userRef = useRef();
    const [user, setUser] = useState('');
    const [userName, setUserName] = useState('');
    const [password, setPassword] = useState('');
    const [validPassword, setValidPassword] = useState(false);
    const [passwordFocus, setPasswordFocus] = useState(false);
    const [confirmPassword, serConfirmePassword] = useState('');
    const [validConfirm, setValidConfirm] = useState(false);
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');


    useEffect(() => {
        userRef.current.focus();
    }, []);

    useEffect(() => {
        const result = PWD_REGEX.test(password);
        setValidPassword(result);

        const match = password === confirmPassword;
        setValidConfirm(match);
    }, [password, confirmPassword]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        const User = {
            'username': userName,
            'password': password,
            'password2': confirmPassword,
            'email': user,
            'first_name': firstName,
            'last_name': lastName,
        };
        try {
            const response = await axios.post(registerUrl, User);
            alert('Usuário cadastrado com sucesso!');
            setTela(1);
        } catch (err) {
            // console.log(err);
            alert("Erro ao fazer cadastro");
        }
    }

    return (
        <section className={styles.register}>
            <Form className={`${styles['register-forms']} p-5 rounded`} onSubmit={handleSubmit}>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupUser">
                    <Form.Label>Nome de usuário</Form.Label>
                    <Form.Control ref={userRef} type="text" placeholder="Digite seu nome de usuário"
                        onChange={(e) => setUserName(e.target.value)}
                        value={userName} />
                </Form.Group>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupEmail">
                    <Form.Label>Email</Form.Label>
                    <Form.Control type="email" placeholder="Digite seu email"
                        onChange={(e) => setUser(e.target.value)}
                        value={user} />
                </Form.Group>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupPas">
                    <Form.Label>Senha
                        {validPassword ? <FcCheckmark size={20} />
                            : <HiXMark className={styles.iconRed} size={20} />}
                    </Form.Label>

                    <Form.Control type="password" placeholder="Digite sua senha"
                        onChange={(e) => setPassword(e.target.value)}
                        value={password}
                        onFocus={() => setPasswordFocus(true)}
                        onBlur={() => setPasswordFocus(false)} />
                    <div className={passwordFocus && !validPassword ? styles.show : styles.hide}>
                        <FaInfoCircle /> A senha tem que ter
                        <div>8 ou mais digitos</div>
                        <div>1 Caractere especial</div>
                        <div>1 Letra maiúscula e 1 minuscula</div>
                        <div>1 número</div>
                    </div>
                </Form.Group>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupConPas">
                    <Form.Label>Repita sua senha
                        {validConfirm && validPassword ? <FcCheckmark size={20} />
                            : <HiXMark className={styles.iconRed} size={20} />}
                    </Form.Label>
                    <Form.Control type="password" placeholder="Repita sua senha"
                        onChange={(e) => serConfirmePassword(e.target.value)}
                        value={confirmPassword} />
                </Form.Group>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupConFirName">
                    <Form.Label>Primeiro nome</Form.Label>
                    <Form.Control type="text" placeholder="Digite seu primeiro nome"
                        onChange={(e) => setFirstName(e.target.value)}
                        value={firstName} />
                </Form.Group>

                <Form.Group className={`${styles.formGroupCtm} mb-3`} controlId="formGroupConLastName">
                    <Form.Label>Último nome</Form.Label>
                    <Form.Control type="text" placeholder="Digite seu último nome"
                        onChange={(e) => setLastName(e.target.value)}
                        value={lastName} />
                </Form.Group>

                <Button variant="" type="submit"
                    id={styles.buttonCtm}
                    disabled={!validPassword || !validConfirm}>
                    Registrar
                </Button>
            </Form>
        </section>
    );
}

export default Register;