* {
  font-family: "Montserrat", sans-serif;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.app-main{
  display: flex;
  flex-direction: column; 
  min-height: 100vh;
}

h5 {
  font-weight: bold !important;
}

.tela-entrada {
  height: 90px;
  background-color: #13D8A3;
  color: white;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
}

.logo-container .logo {
  height: 70px;
}

.abas-container {
  display: flex;
  gap: 20px;
  font-size: 17px;
  align-items: center;
}
.abas-container p {
  margin: 0;
}
.tab {
  cursor: pointer;
  color: white;
  font-weight: bold;
  padding: 8px 16px;
  transition: background-color 0.3s;
}

.tab:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.tab.active {
  border-bottom: 2px solid white;
}

@media (max-width: 480px) {
  .tela-entrada {
    top: 10px;
    right: 10px;
  }

  .tela-entrada .tab {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}


.logos-bottom-left {
  position: fixed;
  bottom: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 8px;
  z-index: 1000;
  transition: background-color 0.2s ease;
}

.logos-bottom-left:hover {
  background-color: #f5f5f5;
}

.logos-bottom-left .logo {
  height: 80px;
  width: auto;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.logos-bottom-left .logo:hover {
  transform: scale(1.05);
}

.logos-bottom-left .separator {
  width: 2px;
  height: 60px;
  background-color: #cccccc;
  transition: background-color 0.2s ease, height 0.2s ease;
}

.logos-bottom-left:hover .separator {
  background-color: #999999;
  height: 80px;
}

@media (max-width: 600px) {
  .logos-bottom-left {
    bottom: 10px;
    left: 10px;
    gap: 12px;
    padding: 6px 10px;
  }

  .logos-bottom-left .logo {
    height: 60px;
  }

  .logos-bottom-left .separator {
    height: 48px;
  }
}