// src/components/PersonalScreen.jsx
import React from 'react';
import Draggable from 'react-draggable';
import './stylePersonal.css';
import { CiPen } from "react-icons/ci";

const PersonalScreen = ({ isOpen, onRequestClose, lidarFree }) => {
  if (!isOpen) {
    return null;
  }

  const handleClickFree = () => {
    lidarFree();
  };

  return (
    <Draggable handle=".panel-header" bounds="body">
      <div className="draggable-panel">
        <div className="panel-header">
          <h2>Personalização</h2>
          <button onClick={onRequestClose}>Fechar</button>
        </div>
        <div className="panel-content">
          <CiPen onClick={handleClickFree} size={30} />
        </div>
      </div>
    </Draggable>
  );
};

export default PersonalScreen;
