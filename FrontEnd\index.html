<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/L-a.jpg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LisaWeb</title>
  <!--Font-->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
    rel="stylesheet">
</head>

<body>
  <div id="root"></div>

  <!-- Carrega a biblioteca Cornerstone Core da CDN -->
  <script src="https://unpkg.com/cornerstone-core"></script>
  <script
    src="https://cdn.jsdelivr.net/npm/cornerstone-wado-image-loader@4.13.2/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
  <!-- <PERSON>eg<PERSON> o código principal do React como módulo -->
  <script type="module" src="/src/main.jsx"></script>

</body>

</html>