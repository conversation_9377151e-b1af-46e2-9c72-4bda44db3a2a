import "./Forgot-password.css";
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
import { useState, useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import axios from '../../api/axios';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';

function ForgotPassword() {
    const [email, setEmail] = useState("");
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState({ text: "", type: "" });
    const emailRef = useRef(null);

    // URL para solicitar recuperação de senha
    const forgotPasswordUrl = '/api/v1/password-reset/';

    useEffect(() => {
        emailRef.current.focus();
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setMessage({ text: "", type: "" });

        if (!email) {
            setMessage({ text: "Por favor, informe seu e-mail", type: "danger" });
            setLoading(false);
            return;
        }

        try {
            // Enviar solicitação de recuperação de senha
            await axios.post(forgotPasswordUrl, { email });
            setMessage({
                text: "Se o seu email existe no sistema, o link de recuperação foi enviado",
                type: "success"
            });
            setEmail("");
        } catch (err) {
            console.error("Erro ao solicitar recuperação de senha:", err);
            // Usar a mesma mensagem para não revelar se o email existe ou não
            setMessage({
                text: "Se o seu email existe no sistema, o link de recuperação foi enviado",
                type: "success"
            });
        } finally {
            setLoading(false);
        }
    }

    return (
        <main className="forgotPassword">
            <Form className={`p-5 rounded forgotPasswordForm`} onSubmit={handleSubmit}>
                <h2>Esqueceu a senha?</h2>
                <p>Não se preocupe, será enviado um e-mail para você. Siga as instruções no e-mail para criar uma nova senha e recuperar o acesso à sua conta.</p>

                {message.text && (
                    <Alert variant={message.type} className="mb-3">
                        {message.text}
                    </Alert>
                )}

                <Form.Group className="mb-3" controlId="formGroupCtm">
                    <Form.Label>E-mail</Form.Label>
                    <Form.Control
                        ref={emailRef}
                        type="email"
                        placeholder="Digite seu e-mail"
                        onChange={(e) => setEmail(e.target.value)}
                        value={email}
                        required
                    />
                </Form.Group>

                <Form.Group className={`mb-3`} controlId="formGroupCtmChe">
                    <span><Link to='/'>Voltar para login</Link></span>
                </Form.Group>

                <Button
                    variant=""
                    type="submit"
                    id="buttonCtm"
                    disabled={loading}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Enviando...
                        </>
                    ) : (
                        "Enviar e-mail"
                    )}
                </Button>
            </Form>
        </main>
    );
}

export default ForgotPassword;