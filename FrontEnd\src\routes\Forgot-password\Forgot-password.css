.forgotPassword {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #EBE5E0;
}

.forgotPasswordForm{
    background-color: white;
    width: 450px;
    min-width: 400px;
    border: 2px solid #13D8A3;
    border-radius: 20px;
}

.forgotPasswordForm h2{
    text-align: center;
    font-weight: 600;
    font-size: 2rem;
    margin-top: -10px;
    color: #333;
}

.forgotPasswordForm p{
    text-align: center;
}

#formGroupCtmChe:focus {
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

#formGroupCtmChe:checked {
    border: 0;
    background-color: #13D8A3;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

#buttonCtm {
    background-color: #13D8A3;
    color: white;
    width: 100%;
    transition: background-color 0.3s;
}

#buttonCtm:hover:not(:disabled) {
    background-color: #10c090;
}

#buttonCtm:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

#formGroupCtm:focus {
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

.forgotPasswordForm a {
    color: #13D8A3;
    text-decoration: underline !important;
    text-underline-offset: 3px;
}

@media (max-width:600px) {
    .forgotPasswordForm {
        min-width: 300px;
    }
}