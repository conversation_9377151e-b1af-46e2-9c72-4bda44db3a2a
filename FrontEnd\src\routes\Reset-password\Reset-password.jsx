import "./Reset-password.css";
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
import { useState, useRef, useEffect } from "react";
import { Link, useNavigate, useLocation, useParams } from "react-router-dom";
import axios from '../../api/axios';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaInfoCircle, FaCheck, FaTimes } from "react-icons/fa";

// Regex para validação de senha
const PWD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%-+]).{8,24}$/;

function ResetPassword() {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();

    // Extrair token e uid da URL (tanto de parâmetros de caminho quanto de consulta)
    const searchParams = new URLSearchParams(location.search);
    const tokenFromQuery = searchParams.get('token');
    const uidFromQuery = searchParams.get('uid');

    // Usar parâmetros do caminho se disponíveis, caso contrário usar parâmetros de consulta
    const token = params.token || tokenFromQuery;
    const uid = params.uid || uidFromQuery;

    const [password, setPassword] = useState('');
    const [validPassword, setValidPassword] = useState(false);
    const [passwordFocus, setPasswordFocus] = useState(false);

    const [confirmPassword, setConfirmPassword] = useState('');
    const [validConfirm, setValidConfirm] = useState(false);

    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState({ text: "", type: "" });

    const passwordRef = useRef(null);

    // URL para confirmar a redefinição de senha
    const resetPasswordUrl = '/api/v1/password-reset/confirm/';

    useEffect(() => {
        // Log para depuração
        console.log("Parâmetros da URL:", {
            params,
            token,
            uid,
            tokenFromQuery,
            uidFromQuery,
            pathname: location.pathname
        });

        // Verificar se token e uid estão presentes
        if (!token || !uid) {
            setMessage({
                text: "Link de redefinição de senha inválido ou expirado.",
                type: "danger"
            });
        }

        passwordRef.current.focus();
    }, [token, uid, params, tokenFromQuery, uidFromQuery, location.pathname]);

    // Validar senha
    useEffect(() => {
        const result = PWD_REGEX.test(password);
        setValidPassword(result);

        // Validar confirmação de senha
        const match = password === confirmPassword;
        setValidConfirm(match);
    }, [password, confirmPassword]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setMessage({ text: "", type: "" });

        // Verificar se a senha é válida
        if (!validPassword || !validConfirm) {
            setMessage({
                text: "Por favor, verifique se a senha atende aos requisitos e se as senhas coincidem.",
                type: "danger"
            });
            setLoading(false);
            return;
        }

        try {
            // Enviar solicitação para redefinir a senha
            await axios.post(resetPasswordUrl, {
                password,
                password2: confirmPassword,
                token,
                uid
            });

            setMessage({
                text: "Senha redefinida com sucesso! Você será redirecionado para a página de login.",
                type: "success"
            });

            // Redirecionar para a página de login após 3 segundos
            setTimeout(() => {
                navigate('/');
            }, 3000);

        } catch (err) {
            console.error("Erro ao redefinir senha:", err);
            let errorMessage = "Erro ao redefinir senha. O link pode ter expirado ou ser inválido.";

            // Verificar se há mensagens de erro específicas na resposta
            if (err.response && err.response.data) {
                if (err.response.data.password) {
                    errorMessage = `Erro na senha: ${err.response.data.password.join(', ')}`;
                } else if (err.response.data.detail) {
                    errorMessage = err.response.data.detail;
                }
            }

            setMessage({
                text: errorMessage,
                type: "danger"
            });
        } finally {
            setLoading(false);
        }
    }

    return (
        <main className="resetPassword">
            <Form className={`p-5 rounded resetPasswordForm`} onSubmit={handleSubmit}>
                <h2>Redefinir Senha</h2>
                <p>Digite sua nova senha abaixo.</p>

                {message.text && (
                    <Alert variant={message.type} className="mb-3">
                        {message.text}
                    </Alert>
                )}

                <Form.Group className="mb-3" controlId="formGroupPassword">
                    <Form.Label>
                        Nova Senha
                        {validPassword ? (
                            <FaCheck className="text-success ms-2" />
                        ) : (
                            <FaTimes className="text-danger ms-2" />
                        )}
                    </Form.Label>
                    <Form.Control
                        ref={passwordRef}
                        type="password"
                        placeholder="Digite sua nova senha"
                        onChange={(e) => setPassword(e.target.value)}
                        value={password}
                        onFocus={() => setPasswordFocus(true)}
                        onBlur={() => setPasswordFocus(false)}
                        required
                    />
                    <div className={passwordFocus && !validPassword ? "passwordRequirements show" : "passwordRequirements hide"}>
                        <FaInfoCircle /> A senha deve ter:
                        <ul>
                            <li>8 a 24 caracteres</li>
                            <li>Pelo menos uma letra maiúscula</li>
                            <li>Pelo menos uma letra minúscula</li>
                            <li>Pelo menos um número</li>
                            <li>Pelo menos um caractere especial (!@#$%-+)</li>
                        </ul>
                    </div>
                </Form.Group>

                <Form.Group className="mb-3" controlId="formGroupConfirmPassword">
                    <Form.Label>
                        Confirmar Senha
                        {validConfirm && validPassword ? (
                            <FaCheck className="text-success ms-2" />
                        ) : (
                            <FaTimes className="text-danger ms-2" />
                        )}
                    </Form.Label>
                    <Form.Control
                        type="password"
                        placeholder="Confirme sua nova senha"
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        value={confirmPassword}
                        required
                    />
                </Form.Group>

                <Form.Group className={`mb-3`} controlId="formGroupCtmChe">
                    <span><Link to='/'>Voltar para login</Link></span>
                </Form.Group>

                <Button
                    variant=""
                    type="submit"
                    id="buttonCtm"
                    disabled={loading || !validPassword || !validConfirm}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Processando...
                        </>
                    ) : (
                        "Redefinir Senha"
                    )}
                </Button>
            </Form>
        </main>
    );
}

export default ResetPassword;
