//react
import { useState, useEffect, useContext, useRef } from 'react';
import Draggable from 'react-draggable';
import Swal from 'sweetalert2'
//Css
import '../style.css';
import './Screen.css';
import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';
//Cornerstone
import cornerstone from 'cornerstone-core';
import cornerstoneWADOImageLoader from 'cornerstone-wado-image-loader';
import dicomParser from 'dicom-parser';
import cornerstoneTools from 'cornerstone-tools';
//axios
import axios from '../../api/axios.jsx';
//context
import { AuthContext } from '../../context/AuthProvider.jsx';
import ProgressBar from 'react-bootstrap/ProgressBar';
import { TailSpin } from 'react-loader-spinner';
//utils
import { handleDrop } from '../../utils/handleDrop.jsx';
import { handleDragOver } from '../../utils/handleDragOver.jsx';
import { configurarCornerstone } from '../../utils/Cornerstone/configurarCornerstone.jsx';
import { handleJanelamento } from '../../utils/Janelamento/janelamento.jsx';
import { handleAeracao } from '../../utils/Aeracao/aeracao.jsx';
//components
import { Tollbar } from '../Tollbar/Tollbar.jsx';
import { SideMenu } from '../SideMenu/SideMenu.jsx';


function Screen({ idTela, selecionada, onClick, algoritmo, executarSegmentacao, setExecutarSegmentacao, sidebarVisivel, aerationCustom, setAerationCustom, setReportNotes, setDoctorCRM }) {
  // states
  const [mostrarOk, setMostrarOk] = useState(false);
  const [arquivos, setArquivos] = useState([]);
  const [isLengthToolActive, setIsLengthToolActive] = useState(false);
  const [isZoomToolActive, setIsZoomToolActive] = useState(false);
  const [isFreeToolActive, setIsFreeToolActive] = useState(false);
  const [isEraserToolActive, setIsEraserToolActive] = useState(false);
  const [pacientInfos, setPacientInfos] = useState({});
  const [imagesId, setImagesId] = useState([]);
  const backendUrl = "/api/v1/series/segmentation/new/";
  const { authData } = useContext(AuthContext);
  const scrollContainerRef = useRef(null);
  const [indiceImagemAtual, setIndiceImagemAtual] = useState('');
  const [imagemAtual, setImagemAtual] = useState('');
  const [jaSegmentada, setJaSegmentada] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [mobilenetResult, setMobilenetResult] = useState(null);
  const [vgg19bnResult, setVgg19bnResult] = useState(null);
  const [resNet34Result, setResNet34Result] = useState(null);
  const [efficientnetb7Result, setEfficientNetB7Result] = useState(null);
  const [showModalClassification, setShowModalClassification] = useState(false);
  const [loading, setLoading] = useState(false);
  const [contornosFiltrados, setFiltrados] = useState([]);
  const [ativadoCorrecao, setAtivadoCorrecao] = useState(false);
  const [ativadoEnviar, setAtivadoEnviar] = useState(false);
  const [sliceParams, setSliceParams] = useState(null);
  const [seriesParams, setSeriesParams] = useState(null);
  const [sliceThickness, setSliceThickness] = useState(null);
  // janelamento e aeração
  function ensureHttps(address) {
    if (!address) return address;
    try {
      const url = new URL(address, window.location.origin);
      url.protocol = window.location.protocol;
      return url.toString();
    } catch {
      return address;
    }
  }
  handleJanelamento(selecionada, idTela);
  handleAeracao(contornosFiltrados, selecionada, arquivos, idTela, aerationCustom);

  useEffect(() => {
    configurarCornerstone(idTela);
  }, []);

  const toggleFullScreen = () => {
    if (!isFullScreen) {
      // Entra no modo tela cheia
      if (scrollContainerRef.current.requestFullscreen) {
        scrollContainerRef.current.requestFullscreen();
      }
    } else {
      // Sai do modo tela cheia
      if (document.fullscreenElement) {
        document.exitFullscreen();
      }
    }
    setIsFullScreen(!isFullScreen);
  };


  //toda ver que a sidebar mudar vai redimensionar
  useEffect(() => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.resize(element);
  }, [sidebarVisivel]);

  function desenharExame(contoursList) {
    const element = document.getElementById(`dicomTela${idTela}`);
    let currentIndex = 0;

    function mostrarImagem() {
      const imageId = imagesId[currentIndex];
      cornerstone.loadImage(imageId).then((image) => {
        cornerstone.displayImage(element, image);
        desenharPontos(element, contoursList[currentIndex]);

        currentIndex++;

        if (currentIndex < imagesId.length) {
          // Ainda não é a última, continua para a próxima
          setTimeout(mostrarImagem, 1); // Exibe a próxima após 1 segundo
        } else {
          // Ao chegar na última imagem, volta para a primeira
          currentIndex = 0;
          const primeiraImageId = imagesId[currentIndex];
          cornerstone.loadImage(primeiraImageId).then((firstImage) => {
            cornerstone.displayImage(element, firstImage);
            desenharPontos(element, contoursList[currentIndex]);
            // Aqui não chamamos mais mostrarImagem(), logo não há loop infinito.
          }).catch(err => console.error(err));
        }
      }).catch(err => console.error(err));
    }

    mostrarImagem();
  }


  //desenhar os pontos
  function desenharPontos(element, pontos) {
    //cornerstoneTools.textStyle.setFontSize(0.00000001); // Exemplo: 18px
    // Verifica se 'pontos' está definido e não é vazio
    if (!pontos || pontos.length === 0) {
      return;
    }

    const image = cornerstone.getImage(element);

    // Itera sobre cada região em 'pontos'
    for (let regiao of pontos) {
      // Verifica se 'regiao' é um array de pontos
      if (Array.isArray(regiao) && regiao.length > 0 && Array.isArray(regiao[0])) {
        // Cria os handles (pontos) da região
        const handles = regiao.map((ponto, index) => {
          const nextIndex = (index + 1) % regiao.length;
          return {
            x: ponto[0],
            y: ponto[1],
            highlight: false,
            active: false,
            index: index,
            locked: false,
            lines: [
              {
                x: regiao[nextIndex][0],
                y: regiao[nextIndex][1]
              }
            ] // Será preenchido posteriormente
          };
        });

        // Cria o objeto de dados específico da ferramenta
        const measurementData = {
          visible: true, // precisa
          active: false,
          invalidated: true,
          handles: {
            points: handles,
            // textBox: {
            //   hasMoved: false, // some e aparece alguns
            //   active: false,
            //   drawnIndependently: true
            // },
            textBox: {
              active: false,
              hasMoved: false,
              movesIndependently: true,
              drawnIndependently: true,
              allowedOutsideImage: true,
              hasBoundingBox: true,
              visible: false
            },
            isClosed: true
          },
          canComplete: false,
          isDrawing: false,
          toolType: 'FreehandRoi',
          color: 'green' // Opcional: define a cor padrão
        };

        // Adiciona o estado da ferramenta ao elemento
        cornerstoneTools.addToolState(element, 'FreehandRoi', measurementData);
        // Atualiza a renderização
        cornerstone.updateImage(element);
        // Obtemos a instância da ferramenta FreehandRoi
        const toolInstance = cornerstoneTools.getToolForElement(element, 'FreehandRoi');

        // Verifica se os métodos de atualização existem
        if (toolInstance && typeof toolInstance.updateCachedStats === 'function') {
          // Recalcula as estatísticas em cache
          toolInstance.updateCachedStats(image, element, measurementData);
        } else {
          // Se o método não existir, pode ser necessário chamar um método alternativo ou atualizar manualmente
          console.log('Método updateCachedStats não encontrado na ferramenta FreehandRoi.');
        }
      }
    }

    cornerstoneTools.setToolPassive('FreehandRoi');
    // Define a espessura da linha (em pixels)
    cornerstoneTools.toolStyle.setToolWidth(3); // Altere o número para o valor desejado
    cornerstoneTools.textStyle.setFontSize(0.000000000000001);
  }

  function corrigirDesenho(e) {
    e.preventDefault();
    // ativando a FreehandRoi para modifcações
    cornerstoneTools.setToolActive('FreehandRoi', { mouseButtonMask: 1 });
    //ativando o botão da FreehandRoi
    setIsFreeToolActive(true);
    //desativando os botões de correção e ativando o de enviar
    setAtivadoCorrecao(false);
    setAtivadoEnviar(true);
    //desativando as outras ferramentas
    setIsLengthToolActive(false);
    setIsZoomToolActive(false);
    setIsEraserToolActive(false);
  }

  async function enviarCorrecaoSegmentacao(e) {
    e.preventDefault();
    cornerstoneTools.setToolPassive('FreehandRoi');
    setIsFreeToolActive(false);
    setAtivadoCorrecao(true);
    setAtivadoEnviar(false);

    const element = document.getElementById(`dicomTela${idTela}`);
    const toolState = cornerstoneTools.getToolState(element, 'FreehandRoi');

    let contornosAtuais = [];
    toolState.data.forEach((tool) => {
      let data = [];
      tool.handles.points.forEach((ponto) => {
        let pontoAtual = [ponto.x, ponto.y];
        data.push(pontoAtual);
      });
      contornosAtuais.push(data);
    });

    const requisition = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access}`
      },
    };

    const contoursAdress = `/api/v1/slice/segmentation/${indiceImagemAtual + 1}/update/`;
    const body = {
      series_instance_uid: pacientInfos.seriesID,
      contours: contornosAtuais
    };

    try {
      const response = await axios.post(contoursAdress, body, requisition);
      console.log(response);

      // Após atualizar a segmentação, chama a função para receber e desenhar os pontos novamente
      await receberNovosPontos();
      setMostrarOk(true);
      setTimeout(() => setMostrarOk(false), 2000);
    } catch (err) {
      console.log(err.response);
    }
  }

  //fazer o carregamento dos arquivos
  useEffect(() => {
    if (arquivos.length > 0) {
      const element = document.getElementById(`dicomTela${idTela}`);
      cornerstone.enable(element)

      // Configuração do Image Loader para usar dicomParser
      cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
      cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
      cornerstoneWADOImageLoader.configure({
        beforeSend: function (xhr) {
          // headers customizados
        },
        //roda em background, aumentando a perfomance
        useWebWorkers: true
      });

      let imageIds = [];
      Array.from(arquivos).forEach((file) => {
        // Verifica se 'file' é do tipo Blob ou File antes de criar a URL
        if (file instanceof File) {
          const fileUrl = URL.createObjectURL(file);
          imageIds.push(`wadouri:${fileUrl}`);
        } else {
          // Transforma as URLs em imageIds com prefixo wadouri:
          imageIds = arquivos.map(fileUrl => `wadouri:${fileUrl}`);
        }
      });

      setImagesId(imageIds);

      // Configura o stack de imagens
      const stack = {
        currentImageIdIndex: 0,
        imageIds: imageIds
      };

      cornerstoneTools.addStackStateManager(element, ['stack']);
      cornerstoneTools.addToolState(element, 'stack', stack);

      cornerstone.loadImage(imageIds[0]).then((image) => {
        cornerstone.displayImage(element, image); // Exibe a primeira imagem

        // Extrair a espessura do slice (slice thickness) dos metadados DICOM
        const imageMetadata = image.data;
        if (imageMetadata && imageMetadata.string) {
          const sliceThicknessValue = imageMetadata.string('x00180050'); // Tag DICOM para Slice Thickness
          setSliceThickness(sliceThicknessValue);
        }
      }).catch(err => console.error(err));

      const getCurrentImage = () => {
        try {
          const image = cornerstone.getImage(element);
          if (image && image.imageId) {
            const imageFileName = image.imageId.substring(8);
            const novoIndice = arquivos.indexOf(imageFileName);

            if (novoIndice !== -1) {
              setIndiceImagemAtual(novoIndice);
              setImagemAtual(`${novoIndice + 1}/${arquivos.length}`);

              // Extrair a espessura do slice (slice thickness) dos metadados DICOM
              const imageMetadata = image.data;
              if (imageMetadata && imageMetadata.string) {
                const sliceThicknessValue = imageMetadata.string('x00180050');
                setSliceThickness(sliceThicknessValue);
              }
            }
          }
        } catch (error) {
          console.warn('Erro ao obter imagem atual:', error);
        }
      };

      // Retirar função de definir janelamento com o botão esquerdo do mouse
      // Pois dá conflito com o slider
      // Quando mexia no slider o janelamento mudava
      cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 0 });

      // Configurar um evento para chamar a função quando necessário
      element.addEventListener('cornerstoneimagerendered', getCurrentImage);

      // Limpeza ao desmontar o componente
      return () => {
        element.removeEventListener('cornerstoneimagerendered', getCurrentImage);
      };
    }

  }, [arquivos]);

  // useEffect para sincronizar o slider quando o índice muda via scroll
  useEffect(() => {
    // Aguarda o próximo ciclo de renderização para garantir que o DOM foi atualizado
    const timeoutId = setTimeout(() => {
      try {
        const dicomElement = document.getElementById(`dicomTela${idTela}`);
        if (dicomElement) {
          const sliderElement = dicomElement.parentElement.querySelector('.sliderSlicesCtm');
          if (sliderElement && indiceImagemAtual !== '' && indiceImagemAtual !== null &&
              Number(sliderElement.value) !== indiceImagemAtual) {
            // Força a atualização do valor do slider apenas se for diferente
            sliderElement.value = indiceImagemAtual;
          }
        }
      } catch (error) {
        console.warn('Erro ao sincronizar slider:', error);
      }
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [indiceImagemAtual, idTela]);

  const trocarImagem = (indice) => {
    const element = document.getElementById(`dicomTela${idTela}`);

    if (imagesId.length === 0 || indice < 0 || indice >= imagesId.length) return;

    cornerstone.loadImage(imagesId[indice]).then((image) => {
      cornerstone.displayImage(element, image);

      // Extrair a espessura do slice (slice thickness) dos metadados DICOM
      const imageMetadata = image.data;
      if (imageMetadata && imageMetadata.string) {
        const sliceThicknessValue = imageMetadata.string('x00180050'); // Tag DICOM para Slice Thickness
        setSliceThickness(sliceThicknessValue);
      }

      setIndiceImagemAtual(indice);
      setImagemAtual(`${indice + 1}/${imagesId.length}`);
    }).catch(err => console.error("Erro ao carregar imagem:", err));
  };

  useEffect(() => {
    if (selecionada && jaSegmentada) {
      setAtivadoCorrecao(true);
      setAtivadoEnviar(false);
    } else {
      setAtivadoCorrecao(false);
    }
  }, [selecionada, jaSegmentada]);

  const deactivateZoomTool = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.reset(element);
  };


  const activateLengthTool = () => {
    cornerstoneTools.setToolActive('Length', { mouseButtonMask: 1 });
    setIsLengthToolActive(true);
  };

  const deactivateLengthTool = () => {
    cornerstoneTools.setToolDisabled('Length');
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.enable(element)
    cornerstoneTools.removeToolState(element, 'Length');
    setIsLengthToolActive(false);
  };

  const activateEraserTool = () => {
    cornerstoneTools.setToolActive('Eraser', { mouseButtonMask: 1 })
    setIsEraserToolActive(true);
    setIsLengthToolActive(false);
    setIsFreeToolActive(false);
  };
  const ******************** = () => {
    cornerstoneTools.setToolDisabled('Eraser')
    setIsEraserToolActive(false);
  };
  const activateFreeTool = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.enable(element);
    cornerstoneTools.setToolActiveForElement(element, 'FreehandRoi', { mouseButtonMask: 1 });
    cornerstoneTools.toolStyle.setToolWidth(3); // Altere o número para o valor desejado
    cornerstoneTools.textStyle.setFontSize(0.000000000000001);
    setIsFreeToolActive(true);
  };

  const deactivateFreeTool = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstoneTools.setToolDisabledForElement(element, 'FreehandRoi');
    setIsFreeToolActive(false);
    cornerstone.enable(element)
    cornerstoneTools.removeToolState(element, 'FreehandRoi');
  };

  const lidarFree = () => {
    if (isFreeToolActive) {
      deactivateFreeTool();
    } else {
      ********************();
      setIsLengthToolActive(false);
      activateFreeTool();
    }
  };

  const lidarEraser = (e) => {
    e.preventDefault();
    if (isEraserToolActive) {
      ********************();
    } else {
      activateEraserTool();
    }
  };

  const lidarRegua = () => {
    if (isLengthToolActive) {
      deactivateLengthTool();
    } else {
      ********************();
      setIsFreeToolActive(false);
      activateLengthTool();
    }
  }
  const lidarZoom = (e) => {
    e.preventDefault();
    e.stopPropagation();
    deactivateZoomTool();
  }

  const lidarLimparTela = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    cornerstone.disable(element);
    // cornerstone.enable(element);
    // setArquivos([]);
    configurarCornerstone();
  }

  const filtrarPontos = (contornos) => {
    // console.log(contornos);
    const pontosFiltrados = contornos.map(element => {
      let temseg = false;

      const segmentoFiltrado = element.map(segment => {
        return segment.filter((pontoAtual, index) => {
          // Obter os pontos limites
          const [x, y] = pontoAtual;
          if (index === 0) return true; // Mantém o primeiro ponto
          const pAnterior = segment[index - 1];

          // Condição de filtragem
          return (Math.abs(pontoAtual[0] - pAnterior[0]) > 1) || (Math.abs(pontoAtual[1] - pAnterior[1]) > 1);
        });
      });

      return segmentoFiltrado;
    });

    setFiltrados({ contornos })
    return pontosFiltrados;
    // return contornos;
  };

  const receberPontosESegmentar = async () => {
    const requisition = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access}`
      },
    };
    //receber contornos
    const contoursAdress = `/api/v1/series/segmentation/${pacientInfos.seriesID}/?algorithm=${algoritmo}`;
    const responseContornos = await axios.get(contoursAdress, requisition);
    // const contornosFiltrados = filtrarPontos()
    const contornosRecebidos = responseContornos.data.results.contours

    // Inicializa um array de contornos vazio, com o mesmo comprimento que arquivos
    const arrayDeContornos = Array.from({ length: arquivos.length }, () => []);
    let requisitionAdress = ensureHttps(responseContornos.data.next);

    // recebe as segmentações com paginação
    while (requisitionAdress !== null) {
      const responseRest = await axios.get(requisitionAdress, requisition);
      requisitionAdress = ensureHttps(responseRest.data.next);
      // const filtrados = filtrarPontos();
      //bug era que sem spread tava adicionando toda a requisição como posição do array
      contornosRecebidos.push(...responseRest.data.results.contours);
    }

    // Popula o array de acordo com o índice da slice
    contornosRecebidos.forEach(({ slice_index, contours_points }) => {
      arrayDeContornos[slice_index] = contours_points;
    });

    //desenhar
    desenharExame(filtrarPontos(arrayDeContornos));
  }

  const receberNovosPontos = async () => {
    const requisition = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access}`
      },
    };
    //receber contornos
    const contoursAdress = `/api/v1/series/segmentation/${pacientInfos.seriesID}/`;
    const responseContornos = await axios.get(contoursAdress, requisition);
    // const contornosFiltrados = filtrarPontos()
    const contornosRecebidos = responseContornos.data.results.contours

    let requisitionAdress = ensureHttps(responseContornos.data.next);

    while (requisitionAdress !== null) {
      const responseRest = await axios.get(requisitionAdress, requisition);
      requisitionAdress = ensureHttps(responseRest.data.next);
      // const filtrados = filtrarPontos();
      //bug era que sem spread tava adicionando toda a requisição como posição do array
      contornosRecebidos.push(...responseRest.data.results.contours);
    }

    // Inicializa um array de contornos vazio, com o mesmo comprimento que arquivos
    const arrayDeContornos = Array.from({ length: arquivos.length }, () => []);
    // Popula o array de acordo com o índice da slice
    contornosRecebidos.forEach(({ slice_index, contours_points }) => {
      arrayDeContornos[slice_index] = contours_points;
    });

    //desenhar
    filtrarPontos(arrayDeContornos);
  }

  // useEffect(() => {
  //   console.log(pixeisLimites);
  // }, [pixeisLimites]);
  const zoomIn = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    // const elementRef = useRef(null);
    // const element = elementRef.current;
    const viewport = cornerstone.getViewport(element);
    viewport.scale += 0.1; // Aumenta o fator de zoom
    cornerstone.setViewport(element, viewport);
  };

  const zoomOut = () => {
    const element = document.getElementById(`dicomTela${idTela}`);
    // const elementRef = useRef(null);
    // const element = elementRef.current;
    const viewport = cornerstone.getViewport(element);
    viewport.scale -= 0.1; // Diminui o fator de zoom
    cornerstone.setViewport(element, viewport);
  };

  // dentro do seu componente React
const chamarParams = async () => {
  const requisition = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${authData.access}`,
    },
  };

  try {
    /* -------- 1) Busca os parâmetros da série -------- */
    const seriesUrl = `/api/v1/series/${pacientInfos.seriesID}/params/`;
    const { data: seriesParams } = await axios.get(seriesUrl, requisition);
    setSeriesParams(seriesParams);               // <- crie/use esse state

    /* -------- 2) Busca os parâmetros de TODAS as slices -------- */
    let sliceUrl = `/api/v1/series/${pacientInfos.seriesID}/slices/params/`;
    const allSliceParams = [];

    // primeira página
    let { data } = await axios.get(sliceUrl, requisition);
    allSliceParams.push(...data.results);
    sliceUrl = data.next;

    // itera sobre as próximas páginas, se existirem
    while (sliceUrl) {
      ({ data } = await axios.get(sliceUrl, requisition));
      allSliceParams.push(...data.results);
      sliceUrl = data.next;
    }

    setSliceParams(allSliceParams);

    /* -------- log opcional -------- */
    console.log('Parâmetros da série:', seriesParams);
    console.log('Parâmetros das slices:', allSliceParams);

  } catch (err) {
    console.error('Erro ao buscar parâmetros:', err);
  }
  };




  const fazerSegmentacao = async () => {
    const requisition = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.access}`
      },
    };

    if (algoritmo === 1) {
      if (jaSegmentada) {
        Swal.fire({
          title: 'Erro!',
          text: 'Atenção, o exame já está segmentado!',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: 'hsl(133, 100%, 30%)'
        });
        return;
      }

      try {
        setLoading(true);
        // Enviar para segmentação
        await axios.post(backendUrl, {
          series_instance_uid: pacientInfos.seriesID,
          patient_id: pacientInfos.patientId,
        }, requisition);

        // Executa a segmentação e aguarda a finalização
        await receberPontosESegmentar();
        setJaSegmentada(true);

        // Após a segmentação, chama o endpoint de params das slices e armazena no state
        await chamarParams();

      } catch (err) {
        if (err.response && err.response.status === 304) {
          await receberPontosESegmentar();
          setJaSegmentada(true);
          await chamarParams();
        } else {
          console.error("Erro na segmentação:", err);
        }
      } finally {
        setLoading(false);
      }
    }
    if (algoritmo === 2) {
      try {
        setLoading(true);

        // Verifica se os dados do paciente estão preenchidos
        if (!pacientInfos.patientId || !pacientInfos.seriesID) {
          console.error("Os dados do paciente não foram definidos:", pacientInfos);
          setLoading(false);
          return;
        }
        console.log(pacientInfos.patientId)
        // Monta a URL com os parâmetros na query string
        const classificationUrl = `/api/v1/series/classification/new/?patient_id=${encodeURIComponent(pacientInfos.patientId)}&series_instance_uid=${encodeURIComponent(pacientInfos.seriesID)}`;


        console.log("Chamando classificação com a URL:", classificationUrl);

        // Realiza a requisição GET utilizando axios.get
        const response = await axios.get(classificationUrl, requisition);
        console.log("Resposta da classificação:", response);

        // Processamento dos resultados recebidos
        const dados = response.data;
        const models = dados.models;
        let mobilenetResultData = {};
        let vgg19bnResultData = {};
        let resNet34ResultData = {};
        let efficientnetb7Result = {};

        models.forEach(model => {
          switch (model.name) {
            case "mobilenet":
              mobilenetResultData = {
                specialization: model.specialization,
                class: model.class,
                trust: model.trust
              };
              break;
            case "vgg19bn":
              vgg19bnResultData = {
                specialization: model.specialization,
                class: model.class,
                trust: model.trust
              };
              break;
            case "resnet34":
              resNet34ResultData = {
                specialization: model.specialization,
                class: model.class,
                trust: model.trust
              };
              break;
            case "efficientnetb7":
              efficientnetb7Result = {
                specialization: model.specialization,
                class: model.class,
                trust: model.trust
              };
              break;
            default:
              break;
          }
        });

        // Atualiza os estados com os resultados
        setMobilenetResult(mobilenetResultData);
        setVgg19bnResult(vgg19bnResultData);
        setResNet34Result(resNet34ResultData);
        setEfficientNetB7Result(efficientnetb7Result);

        // Exibe o modal com os resultados
        setShowModalClassification(true);
        setLoading(false);

      } catch (err) {
        console.error("Erro na classificação:", err);
        setLoading(false);
      }
    }
  };


  useEffect(() => {
    if (executarSegmentacao && selecionada) {
      fazerSegmentacao();
      setExecutarSegmentacao(false);
    } else if (executarSegmentacao) {
      setExecutarSegmentacao(false);
    }
  }, [executarSegmentacao, selecionada]);


  return (
    <section className={`${selecionada == true ? 'selected-ctm' : 'tela-border-ctm'} tela-width-ctm`}
      ref={scrollContainerRef}
      onDragOver={(e) => handleDragOver(e)}
      onDrop={(e) => handleDrop(e, setJaSegmentada, setPacientInfos, setArquivos)}>
      {loading && (
        <div className="spinner-overlay">
          <TailSpin
            height="80"
            width="80"
            color="#4fa94d"
            ariaLabel="tail-spin-loading"
            radius="1"
            visible={true}
          />
        </div>
      )}

      <Tollbar
        enviarCorrecaoSegmentacao={enviarCorrecaoSegmentacao}
        imagemAtual={imagemAtual}
        lidarRegua={lidarRegua}
        isLengthToolActive={isLengthToolActive}
        lidarFree={lidarFree}
        isFreeToolActive={isFreeToolActive}
        lidarZoom={lidarZoom}
        isZoomToolActive={isZoomToolActive}
        zoomIn={zoomIn}
        zoomOut={zoomOut}
        lidarEraser={lidarEraser}
        toggleFullScreen={toggleFullScreen}
        isEraserToolActive={isEraserToolActive}
      />
      <div id={`dicomTela${idTela}`} className="tela-ctm"
        onClick={() => onClick(idTela)}>
        {arquivos.length !== 0 ?
          <input
            type="range"
            min="0"
            max={imagesId.length - 1}
            value={indiceImagemAtual}
            onChange={(e) => {
              e.stopPropagation();
              e.preventDefault();
              trocarImagem(Number(e.target.value))
            }
            }
            className="sliderSlicesCtm"
          />
          : <></>
        }
      </div>
      {mostrarOk && (
        <div className="sinal-ok">
          ✓ Correção enviada!
        </div>
      )}

      <SideMenu
        pacientInfos={pacientInfos}
        sliceParams={sliceParams}
        seriesParams={seriesParams}
        currentSliceIndex={indiceImagemAtual + 1} // Se indiceImagemAtual inicia em 0
        sliceThickness={sliceThickness}
        onNotesChange={setReportNotes}
        onCRMChange={setDoctorCRM}
      />

      {showModalClassification && (
        <Draggable handle=".modal-header">
          <div
            className="modal show modal-ctm"
            style={{
              display: 'block',
              position: 'fixed',
              top: '35%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1000
            }}
          >
            <Modal.Dialog
              style={{
                margin: 0, // Remove margens padrão que podem afetar o posicionamento
                // Adicione outros estilos se necessário
              }}
            >
              <Modal.Header
                className="modal-header"
                style={{
                  cursor: 'move',
                  backgroundColor: '#28a745',
                  color: 'white',
                  position: 'relative',
                  padding: '20px'
                }}
              >
                <Modal.Title>Resultados da Classificação</Modal.Title>
                <Button
                  onClick={() => setShowModalClassification(false)}
                  style={{
                    position: 'absolute',
                    right: '10px',
                    top: '15px',
                    backgroundColor: 'transparent',
                    border: 'none',
                    color: 'white',
                    fontSize: '20px',
                    fontWeight: 'bold',
                    cursor: 'pointer'
                  }}
                >
                  X
                </Button>
              </Modal.Header>
              <div
                style={{
                  padding: '10px',
                  backgroundColor: '#f8d7da',
                  color: '#721c24',
                  textAlign: 'center',
                  fontWeight: 'bold'
                }}
              >
                A porcentagem informada é uma sugestão dada pelo nosso modelo de inteligência artificial , ela representa a confiança do modelo na doença designada.
              </div>

              <Modal.Body>
                <div className="classification-results">
                  {mobilenetResult && (
                    <div className="result-section">
                      <h3>{mobilenetResult.specialization || "Mobilenet"}</h3>
                      {mobilenetResult.trust !== null ? (
                        <ProgressBar
                          now={mobilenetResult.trust}
                          label={`${mobilenetResult.trust.toFixed(2)}%`}
                          variant="success"
                        />
                      ) : (
                        <p>Não detectado</p>
                      )}
                    </div>
                  )}
                  {resNet34Result && (
                    <div className="result-section">
                      <h3>{resNet34Result.specialization || "ResNet34"}</h3>
                      {resNet34Result.trust !== null ? (
                        <ProgressBar
                          now={resNet34Result.trust}
                          label={`${resNet34Result.trust.toFixed(2)}%`}
                          variant="success"
                        />
                      ) : (
                        <p>Não detectado</p>
                      )}
                    </div>
                  )}

                  {vgg19bnResult && (
                    <div className="result-section">
                      <h3>{vgg19bnResult.specialization || "VGG19BN"}</h3>
                      {vgg19bnResult.trust !== null ? (
                        <ProgressBar
                          now={vgg19bnResult.trust}
                          label={`${vgg19bnResult.trust.toFixed(2)}%`}
                          variant="success"
                        />
                      ) : (
                        <p>Não detectado</p>
                      )}
                    </div>
                  )}

                  {efficientnetb7Result && (
                    <div className="result-section">
                      <h3>{efficientnetb7Result.specialization || "EfficientNetB7"}</h3>
                      {efficientnetb7Result.trust !== null ? (
                        <ProgressBar
                          now={efficientnetb7Result.trust}
                          label={`${efficientnetb7Result.trust.toFixed(2)}%`}
                          variant="success"
                        />
                      ) : (
                        <p>Não detectado</p>
                      )}
                    </div>
                  )}
                </div>
              </Modal.Body>
            </Modal.Dialog>
          </div>
        </Draggable>
      )}

    </section>
  );

}

export default Screen