.menu-lateral{
  height: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  user-select: none;
  overflow: hidden;
}

.menu-lateral-abas{
  background-color: hsl(0, 0%, 84%);
  height: 50px;
  display: flex;
  align-items: center;
}

.menu-lateral-abas button{
  height: 100%;
  border: none;
  color: #32cd32;
  background-color: hsl(0, 0%, 84%);
  margin: 5px 5px 0px 5px;
}

.menu-lateral-abas button:disabled{
  cursor:not-allowed;
}

.menu-lateral-tela{
  overflow: hidden;
  background-color: #f0f0f0;
  flex-grow: 1;
  padding-left: 15px;
}

.tela-vazia{
  width:100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding:15px;
  font-weight: 600;
  text-align: center;
}

.menu-lateral-abas .aba-selecionada{
  background-color: #f0f0f0;
  border-radius: 10px 10px 0 0;
  padding: 0 3px 0 3px;
}

.tela-paciente{
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  margin: 15px;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.paciente-header {
color: #2c3e50;
margin: 0 0 20px 0;
font-size: 1.3em;
display: flex;
align-items: center;
gap: 10px;
border-bottom: 2px solid #32cd32;
padding-bottom: 12px;
}

.dado-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: 15px;
margin-top: 15px;
}

.dado-item {
display: flex;
flex-direction: column;
gap: 4px;
}

.dado-label {
color: #7f8c8d;
font-size: 0.9em;
font-weight: 500;
}

.dado-valor {
color: #34495e;
font-size: 1em;
font-weight: 500;
word-break: break-word;
}

.destacado {
color: #32cd32;
font-size: 1.1em;
font-weight: 600;
}

.dados-container {
display: flex;
flex-direction: column;
gap: 15px;
}

.tabela-container {
padding: 15px;
height: 100%;
display: flex;
flex-direction: column;
}

.titulo-tabela {
color: #2c3e50;
margin: 0 0 15px 0;
font-size: 1.2em;
font-weight: 600;
border-bottom: 2px solid #32cd32;
padding-bottom: 8px;
}

.scroll-container {
overflow-x: auto;
flex: 1;
}

.TabelaLung {
border-collapse: collapse;
width: 100%;
min-width: 500px;
font-size: 0.9em;
box-shadow: 0 1px 3px rgba(0,0,0,0.1);
background: white;
}

.TabelaLung th {
background-color: #32cd32;
color: white;
font-weight: 600;
padding: 12px 15px;
text-align: left;
}

.TabelaLung td {
padding: 12px 15px;
border-bottom: 1px solid #f0f0f0;
}

.TabelaLung tr:last-child td {
border-bottom: none;
}

.coluna-metrica {
background-color: #f8f9fa;
font-weight: 500;
color: #2c3e50;
white-space: nowrap;
}

.dado-numerico {
text-align: right;
font-family: 'Montserrat', monospace;
color: #34495e;
}

.destaque {
font-weight: 600;
color: #32cd32;
}

.TabelaLung tbody tr:hover {
background-color: #f8fff8;
cursor: default;
}

.legenda-tabela {
margin-top: 12px;
font-size: 0.8em;
color: #7f8c8d;
text-align: right;
padding: 4px 8px;
}

.view-toggle {
margin-bottom: 12px;
display: flex;
gap: 8px;
}

.view-toggle button {
background-color: #f0f0f0;
color: #32cd32;
border: none;
padding: 8px 16px;
border-radius: 6px;
font-weight: 500;
box-shadow: 0 2px 4px rgba(0,0,0,0.1);
transition: background-color 0.2s, transform 0.1s;
cursor: pointer;
}

.view-toggle button.active {
background-color: #32cd32;
color: #fff;
}

.view-toggle button:hover:not(.active) {
background-color: #e6ffe6;
transform: translateY(-1px);
}
