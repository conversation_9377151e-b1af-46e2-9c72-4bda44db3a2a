//icons
import { Ci<PERSON>uler } from "react-icons/ci";
import { TiZoom } from "react-icons/ti";
import { CiPen } from "react-icons/ci";
import { CiEraser } from "react-icons/ci";
import { FaExpand } from "react-icons/fa";
import { MdOutlineZoomIn } from "react-icons/md";
import { MdOutlineZoomOut } from "react-icons/md";
import { IoMdSend } from "react-icons/io";
//css
import './Tollbar.css';

export const Tollbar = (props) => {

    const { imagemAtual,
        enviarCorrecaoSegmentacao,
        lidarRegua,
        isLengthToolActive,
        lidarFree,
        isFreeToolActive,
        lidarZoom,
        isZoomToolActive,
        zoomIn,
        zoomOut,
        lidarEraser,
        toggleFullScreen,
        isEraserToolActive
    } = props;

    return (
        <div className='icons-ctm'>
            <div className='imagem-atual'>{imagemAtual}</div>

            <div>
                <IoMdSend
                    onClick={(e) => {
                        e.stopPropagation();
                        enviarCorrecaoSegmentacao(e);
                    }}
                    title="Enviar nova segmentação desse slice"
                    size={30}
                    className={`icon-send ${isLengthToolActive ? 'length-active' : 'length-inactive'}`}
                />
            </div>

            <span>
                <CiEraser
                    onClick={lidarEraser}
                    title="Apagar desenho"
                    size={30}
                    className={`icon-eraser ${isEraserToolActive ? 'eraser-active' : 'eraser-inactive'}`}
                />
            </span>

            <div className='eye_tool_pack'>
                <CiPen
                    onClick={lidarFree}
                    title="Desenhar"
                    size={30}
                    className={`icon-free ${isFreeToolActive ? 'free-active' : 'free-inactive'}`}
                />
            </div>
            
            <div className='eye_tool_pack'>
                <CiRuler
                    onClick={lidarRegua}
                    title="Régua"
                    size={30}
                    className={`icon-ruler ${isLengthToolActive ? 'length-active' : 'length-inactive'}`}
                />
            </div>
            
            <span>
                <TiZoom
                    onClick={(e) => lidarZoom(e)}
                    title="Remover zoom"
                    size={30}
                    className={`icon-zoom ${isZoomToolActive ? 'zoom-active' : 'zoom-inactive'}`}
                />
            </span>

            <span>
                <MdOutlineZoomIn
                    onClick={zoomIn}
                    title="Ampliar"
                    size={30}
                    className={`icon-zoom ${isZoomToolActive ? 'zoom-active' : 'zoom-inactive'}`}
                />
            </span>

            <span>
                <MdOutlineZoomOut
                    onClick={zoomOut}
                    title="Afastar"
                    size={30}
                    className={`icon-zoom ${isZoomToolActive ? 'zoom-active' : 'zoom-inactive'}`}
                />
            </span>

            <span>
                <FaExpand
                    size={25}
                    title="Ativar modo tela cheio"
                    className={`icon-expand-screen`}
                    onClick={toggleFullScreen} />
            </span>
        </div>
    );
}