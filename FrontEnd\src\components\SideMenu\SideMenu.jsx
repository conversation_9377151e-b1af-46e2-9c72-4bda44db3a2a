import "./SideMenu.css";
import { useState, useEffect } from "react";

export const SideMenu = ({ pacientInfos, sliceParams, currentSliceIndex, seriesParams, sliceThickness, onNotesChange, onCRMChange }) => {
  const [aba, setAba] = useState(0);
  const [lungView, setLungView] = useState("slice");
  const [notes, setNotes] = useState("");
  const [crm, setCRM] = useState("");

  const [debouncedSliceIndex, setDebouncedSliceIndex] = useState(currentSliceIndex);
  useEffect(() => {
    const t = setTimeout(() => setDebouncedSliceIndex(currentSliceIndex), 100);
    return () => clearTimeout(t);
  }, [currentSliceIndex]);

  const currentSliceData = sliceParams?.[debouncedSliceIndex - 1];

  const seriesResult = seriesParams?.results?.[0];
  const seriesData = seriesResult
    ? {
        left: seriesResult.aggregated_left_lung_result,
        right: seriesResult.aggregated_right_lung_result,
      }
    : null;

  const formatNumber = (n) => (typeof n === "number" ? n.toFixed(3) : n);

  useEffect(() => {
    setAba(pacientInfos?.patientName ? 1 : 0);
  }, [pacientInfos]);

  const formatDicomAge = (ageString) => {
    if (!ageString) return "";
    const val = parseInt(ageString, 10);
    const unit = ageString.slice(-1).toUpperCase();
    const map = { Y: "anos", M: "meses", W: "semanas", D: "dias" };
    return `${val} ${map[unit] || ""}`;
  };

  return (
    <section className="menu-lateral">
      <div className="menu-lateral-abas">
        <button disabled={aba === 0} className={aba === 1 ? "aba-selecionada" : ""} onClick={() => setAba(1)}>Paciente</button>
        {' | '}
        <button disabled={aba === 0} className={aba === 2 ? "aba-selecionada" : ""} onClick={() => setAba(2)}>Pulmão</button>
        {' | '}
        <button disabled={aba === 0} className={aba === 3 ? "aba-selecionada" : ""} onClick={() => setAba(3)}>Doenças</button>
        {' | '}
        <button disabled={aba === 0} className={aba === 4 ? "aba-selecionada" : ""} onClick={() => setAba(4)}>3D</button>
        {' | '}
        <button disabled={aba === 0} className={aba === 5 ? "aba-selecionada" : ""} onClick={() => setAba(5)}>Anotações</button>
      </div>

      <div className="menu-lateral-tela">
        {aba === 0 && <div className="tela-vazia">Nenhum exame foi arrastado.</div>}

        {aba === 1 && (
          <div className="tela-paciente">
            <h3 className="paciente-header">Informações do Paciente</h3>
            <div className="dados-container">
              <div className="dado-item">
                <span className="dado-label">Nome:</span>
                <span className="dado-valor destacado">{pacientInfos?.patientName || "--"}</span>
              </div>
              <div className="dado-item">
                <span className="dado-label">Descrição do Exame:</span>
                <span className="dado-valor">{pacientInfos?.serieDescription || "--"}</span>
              </div>
              <div className="dado-grid">
                <div className="dado-item">
                  <span className="dado-label">Idade:</span>
                  <span className="dado-valor">{pacientInfos?.patientAge ? formatDicomAge(pacientInfos.patientAge) : "--"}</span>
                </div>
                <div className="dado-item">
                  <span className="dado-label">Sexo:</span>
                  <span className="dado-valor">{pacientInfos?.patientSex || "--"}</span>
                </div>
                <div className="dado-item">
                  <span className="dado-label">Data do Exame:</span>
                  <span className="dado-valor">{pacientInfos?.serieData || "--"}</span>
                </div>
                <div className="dado-item">
                  <span className="dado-label">Realizado por:</span>
                  <span className="dado-valor">{pacientInfos?.serieManufatura || "--"}</span>
                </div>
                <div className="dado-item">
                  <span className="dado-label">Espessura do Slice:</span>
                  <span className="dado-valor">{sliceThickness ? `${parseFloat(sliceThickness).toFixed(3)} mm` : "--"}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {aba === 2 && (
          <div className="tabela-container">
            <h3 className="titulo-tabela">Métricas Pulmonares</h3>
            <div className="view-toggle">
              <button className={lungView === "slice" ? "active" : ""} onClick={() => setLungView("slice")}>Slice</button>
              <button className={lungView === "series" ? "active" : ""} onClick={() => setLungView("series")}>Série</button>
            </div>
            <div className="scroll-container">
              {lungView === "slice" && currentSliceData && (
                <>
                  <table className="TabelaLung">
                    <thead>
                      <tr>
                        <th className="coluna-metrica">Métrica</th>
                        <th>Esquerdo</th>
                        <th>Direito</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { label: 'Área (mm)²', key: 'area' },
                        { label: 'Volume (mm)³', key: 'volume' },
                        { label: 'DPM (UH)', key: 'dpm' },
                        { label: 'PERC15 (UH)', key: 'perc15' },
                        { label: 'HiperAerado (%)', key: 'hyper_aerated' },
                        { label: 'NormalmenteAerado (%)', key: 'normally_aerated' },
                        { label: 'PoucoAerado (%)', key: 'little_aerated' },
                        { label: 'NadaAerado (%)', key: 'non_aerated' },
                      ].map(({ label, key }) => {
                        const left = currentSliceData.left_lung_result?.[key] ?? 0;
                        const right = currentSliceData.right_lung_result?.[key] ?? 0;
                        return (
                          <tr key={key}>
                            <td className="coluna-metrica">{label}</td>
                            <td className="dado-numerico">{formatNumber(right)}</td>
                            <td className="dado-numerico">{formatNumber(left)}</td>
                            <td className="dado-numerico">{formatNumber(left + right)}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                  <h3 className="titulo-tabela" style={{ marginTop: 20 }}>Porcentagem de Aeração</h3>
                  <table className="TabelaLung">
                    <thead>
                      <tr>
                        <th className="coluna-metrica">Métrica</th>
                        <th>Valor</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { label: 'Aeração do esquerdo (%)', value: currentSliceData.percentage_aeration?.left_aeration },
                        { label: 'Aeração do direito (%)', value: currentSliceData.percentage_aeration?.right_aeration },
                        { label: 'Aeração total (%)', value: currentSliceData.percentage_aeration?.aeration_total },
                      ].map(({ label, value }) => (
                        <tr key={label}>
                          <td className="coluna-metrica">{label}</td>
                          <td className="dado-numerico">{formatNumber(value ?? 0)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </>
              )}
              {lungView === "series" && seriesData && (
                <>
                  <h4 style={{ marginBottom: 6 }}>Agregado da Série</h4>
                  <table className="TabelaLung">
                    <thead>
                      <tr>
                        <th className="coluna-metrica">Métrica</th>
                        <th>Esquerdo</th>
                        <th>Direito</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { label: 'Área (mm)²', key: 'area' },
                        { label: 'Volume (mm)³', key: 'volume' },
                        { label: 'DPM (UH)', key: 'dpm' },
                        { label: 'PERC15 (UH)', key: 'perc15' },
                        { label: 'Hiper-aerado (%)', key: 'total_hyper_aerated' },
                        { label: 'Normal-aerado (%)', key: 'total_normal_aerated' },
                        { label: 'Pouco-aerado (%)', key: 'total_little_aerated' },
                        { label: 'Não aerado (%)', key: 'total_non_aerated' },
                      ].map(({ label, key }) => {
                        const left = seriesData.right[key] ?? 0;
                        const right = seriesData.left[key] ?? 0;
                        return (
                          <tr key={key}>
                            <td className="coluna-metrica">{label}</td>
                            <td className="dado-numerico">{formatNumber(left)}</td>
                            <td className="dado-numerico">{formatNumber(right)}</td>
                            <td className="dado-numerico">{formatNumber(left + right)}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </>
              )}
              {!currentSliceData && lungView === "slice" && <p>Dados não disponíveis para a slice atual.</p>}
              {!seriesData && lungView === "series" && <p>Dados agregados da série não carregados.</p>}
            </div>
          </div>
        )}

        {aba === 3 && <div className="tela-doencas"><h3>Doenças</h3></div>}
        {aba === 4 && <div className="tela-3d"><h3>Visualização 3D</h3></div>}
        {aba === 5 && (
          <div className="tela-anotacoes" style={{
            padding: '15px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{
              color: '#28a745',
              borderBottom: '2px solid #28a745',
              paddingBottom: '8px',
              marginBottom: '20px'
            }}>Anotações Médicas</h3>

            <div style={{
              marginBottom: '20px',
              backgroundColor: 'white',
              padding: '15px',
              borderRadius: '6px',
              boxShadow: '0 1px 3px rgba(0,0,0,0.08)'
            }}>
              <label
                htmlFor="crm-input"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: 'bold',
                  color: '#495057'
                }}
              >
                <i style={{ marginRight: '5px' }}></i>CRM:
              </label>
              <input
                id="crm-input"
                type="text"
                placeholder="Digite o CRM (apenas números)"
                value={crm}
                onChange={(e) => {
                  // Aceitar apenas números
                  const value = e.target.value.replace(/\D/g, '');
                  setCRM(value);
                  if (onCRMChange) {
                    onCRMChange(value);
                  }
                }}
                style={{
                  width: '100%',
                  padding: '10px',
                  borderRadius: '4px',
                  border: '1px solid #ced4da',
                  fontSize: '16px',
                  transition: 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
                  outline: 'none',
                  ':focus': {
                    borderColor: '#28a745',
                    boxShadow: '0 0 0 0.2rem rgba(40, 167, 69, 0.25)'
                  }
                }}
              />
              <small style={{
                display: 'block',
                marginTop: '5px',
                color: '#6c757d',
                fontSize: '12px'
              }}>
                Informe o número do registro no Conselho Regional de Medicina
              </small>
            </div>

            <div style={{
              marginBottom: '20px',
              backgroundColor: 'white',
              padding: '15px',
              borderRadius: '6px',
              boxShadow: '0 1px 3px rgba(0,0,0,0.08)'
            }}>
              <label
                htmlFor="notes-textarea"
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: 'bold',
                  color: '#495057'
                }}
              >
                <i style={{ marginRight: '5px' }}></i>Observações Clínicas:
              </label>
              <textarea
                id="notes-textarea"
                className="notes-textarea"
                placeholder="Digite suas anotações aqui..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={8}
                style={{
                  width: '100%',
                  marginBottom: '10px',
                  padding: '10px',
                  borderRadius: '4px',
                  border: '1px solid #ced4da',
                  fontSize: '16px',
                  fontFamily: 'inherit',
                  resize: 'vertical',
                  transition: 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
                  outline: 'none'
                }}
              />
              <small style={{
                display: 'block',
                marginTop: '5px',
                color: '#6c757d',
                fontSize: '12px'
              }}>
                Estas observações serão incluídas no relatório final
              </small>
            </div>

            <div style={{ textAlign: 'center' }}>
              <button
                onClick={() => {
                  if (onNotesChange && onCRMChange) {
                    onNotesChange(notes);
                    onCRMChange(crm);
                    alert('Informações salvas com sucesso!');
                  }
                }}
                style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  transition: 'all 0.2s ease-in-out',
                  ':hover': {
                    backgroundColor: '#218838',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
                  }
                }}
              >
                 Salvar Informações
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};
