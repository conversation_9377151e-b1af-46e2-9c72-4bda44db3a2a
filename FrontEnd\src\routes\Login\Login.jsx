//css and bootstrap
import styles from './Login.module.css'
import <PERSON><PERSON> from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
//react router
import { useNavigate, Link } from 'react-router-dom';
//react
import { useState, useContext, useRef, useEffect } from 'react';
//axios
import axios from '../../api/axios';
//context
import { AuthContext } from '../../context/AuthProvider';

function Login() {
    const userRef = useRef();
    const [user, setUser] = useState('');
    const [password, setPassword] = useState('');
    const { authData, setAuthData } = useContext(AuthContext);

    const tokenUrl = '/api/v1/authentication/token/';
    const navigate = useNavigate();

    useEffect(() => {
        userRef.current.focus();

        // Verifica se já existe um token no localStorage
        const storedAuthData = JSON.parse(localStorage.getItem('authData'));

        if (storedAuthData) {
            setAuthData(storedAuthData);
            navigate('/home');
        }
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        const User = {
            'username': user,
            'password': password
        };
        try {
            const response = await axios.post(tokenUrl, User);
            setAuthData(response.data);
            // Armazena o token no localStorage para que permaneça logado
            localStorage.setItem('authData', JSON.stringify(response.data));
            navigate("/home");
        } catch (err) {
            alert("Erro ao fazer login");
        }
    }

    return (
        <section className={styles.login}>
            <Form className={`${styles.loginForms} p-5 rounded`} onSubmit={handleSubmit}>
                <Form.Group className="mb-3" controlId={styles.formGroupCtm}>
                    <Form.Label>Nome de usuário</Form.Label>
                    <Form.Control ref={userRef} type="text" placeholder="Digite seu usuário"
                        onChange={(e) => setUser(e.target.value)}
                        value={user} />
                </Form.Group>

                <Form.Group className="mb-3" controlId={styles.formGroupCtmPas}>
                    <Form.Label>Senha</Form.Label>
                    <Form.Control type="password" placeholder="Digite sua senha"
                        onChange={(e) => setPassword(e.target.value)}
                        value={password} />
                </Form.Group>

                <Form.Group className={`${styles.checkLabel} mb-3`} controlId={styles.formGroupCtmChe}>
                    <span><Form.Check type="checkbox" label="Matenha-me logado" /></span>
                    <span><Link to='/forgot-password'>Esqueceu a senha?</Link></span>
                </Form.Group>

                <Button variant="" type="submit" id={styles.buttonCtm}>
                    Entrar
                </Button>
            </Form>
        </section>
    );
}

export default Login    