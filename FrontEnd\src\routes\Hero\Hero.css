.Hero-main {
    background-color: #EBE5E0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 25px;
}

.Hero-main h1 {
    text-align: center;
    font-size: 50px;
    font-weight: 600;
    margin: 0 100px 0 100px;
    line-height: 1;
    text-transform: uppercase;
    margin-top: 5px;
}

.Hero-main h1 span {
    color: #13D8A3;
}

.Hero-main h2 {
    letter-spacing: 10px;
}

.Hero-buttons {
    display: flex;
    margin-top: 20px;
    gap: 50px;
}

.Hero-buttons button {
    font-weight: 600;
    border-radius: 20px;
    height: 80px;
    width: 180px;
    background-color: transparent;
}

.Hero-graficos {
    margin-top: 10px;
    width: 60%;
}

.Hero-main p {
    margin-top: 10px;
    text-transform: uppercase;
    font-size: 31px;
    font-weight: 700;
}

/* Beneficios e funcionalidades */
.Hero-main .vendas-title{
    margin-top: 50px;
}

.frame-vendas {
    display: flex;
    gap: 60px;
    padding: 50px;
    padding-top: 20px;
    background-color: transparent;
    border-radius: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.imagem-vendas img {
    max-width: 500px;
    width: 100%;
    object-fit: cover;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transition: transform 0.4s ease;
    border: 4px solid #13D8A3;
}

.imagem-vendas img:hover {
    transform: scale(1.03);
}

.beneficios-vendas {
    flex: 1;
}

.beneficios-vendas h2 {
    font-size: 28px;
    margin-bottom: 30px;
    color: #1c1c1c;
}

.beneficios-vendas ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.beneficios-vendas li {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
}

.icone {
    color: #13D8A3;
    font-size: 20px;
    margin-right: 12px;
}