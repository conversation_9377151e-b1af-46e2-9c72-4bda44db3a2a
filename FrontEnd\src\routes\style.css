.home-ctm{
    display: flex;
    align-items:baseline;
    justify-content: center;
    width:100%;
    min-height: 100%;
    position:relative;
}

.section-ctm{
    width: 80%;
    border: 2px solid hsl(0, 0%, 80%);
    display: flex; 
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.section-ctm.expandido {
    width: 100%;
}

.controleSidebar{
    position:absolute;
    bottom:50px;
    left:10px;
    padding: 10px;
    background-color: hsl(133, 100%, 30%);
    border-radius: 10px;
    border: none;
    color: white;
    z-index: 1;
}

.controleSidebar:hover{
    background-color: hsl(133, 100%, 25%);
    transition: background-color 0.4s ease;
}

.sidebarExpandido{
    width: 20%;
    transition: width 0.5s ease;
    min-height: 100vh;
    height: 100%;
}

.sidebarComprimido{
    display: none;
}