.icons-ctm {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    /* Distribui os ícones à esquerda */
    align-items: baseline;
    width: 50px;
    background-color: #f0f0f0;
    height: 100%;
}

.icons-ctm div, .icons-ctm span {
    width: 100%;
    height: 45px;
    margin: 5px 0 5px 0;
    user-select: none;
    display: flex;
    justify-content: center;
}

.icon-send {
    margin-top: 10px;
    color: #32cd32;
}

.icon-eraser{
    color: red;
}

.icon-send,
.icon-ruler,
.icon-pen,
.icon-text,
.length-inactive,
.free-inactive,
.text-inactive,
.icon-expand-screen {
    cursor: pointer;
    transition: background-color 0.3s ease;
    /* Transição suave da cor de fundo ao passar o mouse */
    border-radius: 8px;
    /* Borda arredondada */
}

.zoom-inactive:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    /* Aumenta a sombra ao passar o mouse */
    transform: translateY(-4px);
    /* Leve elevação ao passar o mouse */
    background-color: #000000;
    /* Cor de fundo ao passar o mouse sobre os ícones */
    color: #ffffff;
    /* Cor do ícone ao passar o mouse para branco */
    border-radius: 8px;
    /* Borda arredondada */
    cursor: pointer;
    /* Transforma o cursor em ponteiro ao passar sobre o ícone */
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    /* Transições suaves */
}

.icon-expand-screen:hover,
.icon-send:hover,
.icon-ruler:hover,
.icon-zoom:hover,
.icon-free:hover,
.icon-text:hover,
.length-active,
.text-active {
    background-color: #000000;
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
}

.eraser-active {
    cursor: pointer;
    color: #ffffff;
    background-color: #000000;
    border-radius: 8px;
    margin-right: 10px;
    margin-left: 10px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    /* Aumenta a sombra ao passar o mouse */
    transform: translateY(-4px);
    /* Leve elevação ao passar o mouse */

}

.eraser-inactive:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    transform: translateY(-4px);
    background-color: #000000;
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.free-active {
    background-color: #000000;
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
}



.infos-icons {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 10%;
    height: 90%;
    border-radius: 12px;
    border: 1px solid #ddd;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    cursor: pointer;
}

.info-icon {
    cursor: pointer;
    color: #32cd32;
    transition: background-color 0.3s ease;
    border-radius: 8px;
    padding: 5px;
    font-size: 24px;
}

.info-icon:hover {
    background-color: #000000;
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
}

.imagem-atual {
    position: absolute;
    z-index: 999;
    bottom: 10px;
    font-weight: 500;
    color: #32cd32;
}