* src/components/DraggablePanel.css */
.draggable-panel {
    position: absolute;
    top: 5px; /* Posição inicial no topo */
    left: 5px; /* Posição inicial à esquerda */
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000; /* Ajustar zIndex para garantir que esteja acima do conteúdo */
  }
  
  .panel-header {
    padding: 4px;
    background-color: #007bff;
    color: #fff;
    border-top-left-radius: 1px;
    border-top-right-radius: 1px;
    cursor: move; /* Cursor de movimento ao passar pelo cabeçalho */
  }
  
  .panel-header h2 {
    margin: 0;
  }
  
  .panel-header button {
    background: transparent;
    border: none;
    color: #fff;
    cursor: pointer;
  }
  
  .panel-content {
    padding: 10px;
  }
  