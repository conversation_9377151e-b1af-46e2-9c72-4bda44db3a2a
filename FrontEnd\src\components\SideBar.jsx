//icons
import { FaFile } from "react-icons/fa";
import { IoIosLogOut } from "react-icons/io";
import { FaTrash } from "react-icons/fa";
//css and bootstrap
import styles from './SideBar.module.css';
//react and react router
import { useState, useEffect, useContext, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
//axios
import axios from '../api/axios';
//context
import { AuthContext } from '../context/AuthProvider.jsx';
import { TailSpin } from 'react-loader-spinner';
import { FaHandPaper } from "react-icons/fa";
import { TbReportAnalytics } from "react-icons/tb";



function SideBar({ setAlgoritmo, setExecutarSegmentacao, aerationCustom, setAerationCustom, reportNotes }) {
    const [imageLinks, setImageLinks] = useState([]);
    const backendUrl = '/api/v1/exams/';
    const { authData, setAuthData } = useContext(AuthContext);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const fileInputRef = useRef(null);
    const redirecionarSeNaoLogado = () => {
        const storedAuthData = JSON.parse(localStorage.getItem('authData'));

        if (authData === null && (storedAuthData === undefined || storedAuthData === null)) {
            navigate("/");
        } else if (authData === null || authData === undefined) {
            setAuthData(storedAuthData);
        }
    }

    const handleCustomToggle = () => {
        setAerationCustom((prev) => ({
            ...prev,
            enabled: !prev.enabled,
        }));
    };

    const handleCustomChange = (field, value) => {
        setAerationCustom((prev) => ({
            ...prev,
            [field]: value,
        }));
    };
    useEffect(() => {
        if (fileInputRef.current) {
            fileInputRef.current.setAttribute('webkitdirectory', '');
            fileInputRef.current.setAttribute('directory', '');
        }
    }, []);
    useEffect(() => {
        redirecionarSeNaoLogado();
        ReceberExames();
    }, [authData]);

    //enviar dicoms para o database
    const handleArquivos = async (e) => {
        redirecionarSeNaoLogado();
        const selectedFiles = e.target.files;
        if (authData != null) {
            //ativar spinner
            setLoading(true);

            //passar os dicoms para o formato reconhecido no backend
            const formData = new FormData();
            for (let i = 0; i < selectedFiles.length; i++) {
                formData.append('files[]', selectedFiles[i]);
            }
            //montando a requisição e enviando
            const requisition = {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${authData.access}`
                },
            };
            try {
                await axios.post(backendUrl, formData, requisition);
                //atualizar os exames disponiveis
                ReceberExames();
            } catch (err) {
                alert("nâo foi possivel enviar os exames.");
            } finally {
                setLoading(false); // Desativa o spinner
            }
        }
    }
    /*
        receber os exames que já estão no database
        só é acionado quando entra na home ou o usuário envia dicoms
        então não precisa checar se está logado
    */

    const ReceberExames = async () => {
        setImageLinks([]);
        if (authData?.access) {
            const requisition = {
                headers: {
                    'content-type': 'application/json',
                    'Authorization': `Bearer ${authData.access}`
                },
            };
            try {
                const responseExams = await axios.get(backendUrl, requisition);
                for (let patient of responseExams.data) {

                    const newObj = {
                        name: patient.patient_name,
                        series: []
                    }

                    for (let study of patient.studies) {
                        for (let serie of study.series) {
                            const arraySerieMember = {
                                scans: serie.ct_scans,
                                serieDescription: serie.series_description,
                                patientName: patient.patient_name,
                                patientAge: patient.patient_age,
                                patientSex: patient.patient_sex,
                                patientDicomId: patient.patient_dicom_id,
                                patientId: patient.patient_id,
                                serieData: study.study_date,
                                serieManufatura: study.manufacturer,
                                seriesID: serie.series_instance_uid
                            };
                            newObj.series.push(arraySerieMember);
                        }
                    }

                    setImageLinks(s => [...s, newObj]);
                }

            } catch (err) {
                localStorage.setItem('authData', JSON.stringify(null));
                setAuthData(null);
                console.log(err);
            }
        } else {
            console.log("chave de acesso não definida");
        }

    }
    const handleHUSelection = (e) => {
        const selected = e.target.value;
        switch (selected) {
            case "contrast":
                handleCustomChange("min", 100);
                handleCustomChange("max", 600);
                break;
            case "calcifications":
                handleCustomChange("min", 30);
                handleCustomChange("max", 500);
                break;
            case "hemorrhage":
                handleCustomChange("min", 60);
                handleCustomChange("max", 100);
                break;
            case "gray":
                handleCustomChange("min", 35);
                handleCustomChange("max", 35);
                break;
            case "white":
                handleCustomChange("min", 25);
                handleCustomChange("max", 25);
                break;
            case "muscle":
                handleCustomChange("min", 20);
                handleCustomChange("max", 40);
                break;
            case "water":
                handleCustomChange("min", 0);
                handleCustomChange("max", 0);
                break;
            case "fat":
                // Para "Fat" definindo min = -70 e max = -30
                handleCustomChange("min", -70);
                handleCustomChange("max", -30);
                break;
            case "air":
                // Para "Air" podemos definir ambos como -1000 (ou outro valor de referência)
                handleCustomChange("min", -1000);
                handleCustomChange("max", -1000);
                break;
            default:
                break;
        }
    };

    const logout = () => {
        setAuthData(null);
        localStorage.setItem('authData', JSON.stringify(null));
        navigate("/");
    }

    const deleteExam = async (patientID) => {
        if (authData?.access) {
            const deleteUrl = `/api/v1/patients/delete/${patientID}/`;  // A URL não precisa mais do patientID como parte da rota
            const config = {
                headers: {
                    'Authorization': `Bearer ${authData.access}`,
                    'Content-Type': 'application/json',
                },
            };

            try {
                // Fazer requisição DELETE com o patientID no corpo
                await axios.delete(deleteUrl, config);
                // Atualizar lista de exames após deletar
                ReceberExames();
            } catch (error) {
                console.error('Erro ao deletar exame:', error.response?.data || error.message);
                alert('Erro ao deletar exame');
            }
        } else {
            alert('Você não está autenticado!');
        }
    };
    const handleDownloadRelatorio = async (serieID) => {
        if (!serieID) {
            alert("ID da série não encontrado!");
            return;
        }

        try {
            setLoading(true);
            const dataToSend = {
                series: `${serieID}`, // interpolação DENTRO de crases
                creator: "Outro criador",
                crm: doctorCRM || "123456", // Usa o CRM informado pelo médico
                comments: reportNotes || "Sem anotações."
            };


            const requisition = {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authData.access}`, // Token de autenticação
                },
                responseType: "blob", // Define que a resposta será um arquivo binário
            };

            const response = await axios.post(
                `${backendUrl}relatorio/`,
                dataToSend,
                requisition
            );
            const blob = new Blob([response.data], { type: "application/pdf" });

            // Cria uma URL temporária para o Blob
            const url = URL.createObjectURL(blob);

            // Cria um elemento <a> dinamicamente para forçar o download
            const link = document.createElement("a");
            link.href = url;
            link.download = "relatorio.pdf"; // Nome do arquivo que será baixado
            document.body.appendChild(link);
            link.click(); // Simula o clique para iniciar o download
            document.body.removeChild(link); // Remove o elemento <a>

            // Libera a URL temporária (boa prática)
            URL.revokeObjectURL(url);

            alert("Relatório baixado com sucesso!");

        } catch (error) {
            console.error(error);
            alert("Não foi possível gerar o relatório.");
        } finally {
            setLoading(false);
        }
    };

    // Função para baixar o manual do usuário
    const handleDownloadManual = () => {
        try {
            setLoading(true);

            // Caminho para o arquivo PDF do manual
            const manualPath = '/PROJETO SATCT - ÁREAS DE INTERESSE.pdf';

            // Cria um elemento <a> dinamicamente para forçar o download
            const link = document.createElement("a");
            link.href = manualPath;
            link.download = "MANUAL DO USUÁRIO - SATCT.pdf"; // Nome do arquivo que será baixado
            document.body.appendChild(link);
            link.click(); // Simula o clique para iniciar o download
            document.body.removeChild(link); // Remove o elemento <a>

            alert("Manual do usuário baixado com sucesso!");
        } catch (error) {
            console.error(error);
            alert("Não foi possível baixar o manual do usuário.");
        } finally {
            setLoading(false);
        }
    };



    const deleteSerie = async (serieID) => {
        if (authData?.access) {
            const deleteUrl = `/api/v1/series/delete/${serieID}/`;  // A URL não precisa mais do patientID como parte da rota
            const config = {
                headers: {
                    'Authorization': `Bearer ${authData.access}`,
                    'Content-Type': 'application/json',
                },
            };

            try {
                await axios.delete(deleteUrl, config);
                // Atualizar lista de exames após deletar
                ReceberExames();
            } catch (error) {
                console.error('Erro ao deletar exame:', error.response?.data || error.message);
                alert('Erro ao deletar exame');
            }
        } else {
            alert('Você não está autenticado!');
        }
    };

    return (
        <nav className={`text-center`}>
            <div className={`${styles.sideBarlimite}`}>
                <h2 className='mt-2' id={styles.LisaTextCtm}>
                    SATCT <IoIosLogOut onClick={logout} className={styles.logoutButton} />
                </h2>
                <div className={`${styles.sendDicomsDiv}  mx-2`}>
                    <span className={`${styles.sendDicoms}`}>Upload</span>
                    <label htmlFor='sendDicoms' className={`${styles.sendDicomsLabel} ms-2`}>Escolher Exame</label>
                    <input
                        ref={fileInputRef}
                        type='file'
                        name="sendDicoms"
                        id='sendDicoms'
                        multiple
                        className={styles.sendDicomsCtm}
                        onChange={handleArquivos}
                    />
                </div>

                <div className={`${styles.examsArea} mx-2`}>
                    <h3>Exames</h3>
                    {loading && (
                        <div className={styles.spinnerSidebar}> {/* Centraliza o spinner */}
                            <TailSpin
                                height={80}
                                width={80}
                                color="#00BFFF"
                                ariaLabel="tail-spin-loading"
                                radius="1"
                                wrapperStyle={{}}
                                wrapperClass=""
                                visible={true}
                            />
                        </div>
                    )}
                    {imageLinks.length === 0 ?
                        <div>nenhum exame encontrado</div>
                        :
                        imageLinks.map((elem, key) =>
                            <div key={key}>
                                <div className={styles.serieDiv}>
                                    <span className={styles.serieName}>
                                        {elem.name}
                                    </span>
                                    <FaTrash title="Deletar exames" className={styles.serieDeleteButton} onClick={() => deleteExam(elem.series[0]?.patientId)} />
                                </div>

                                {elem.series.map((serie, index) =>
                                    <div key={index}>
                                        <div className={styles.examLocal}>
                                            <p>{serie.serieDescription}</p>
                                            <div>
                                                <span title="Deletar serie" onClick={() => deleteSerie(serie.seriesID)}><FaTrash /></span>
                                                <span draggable title="Arraste para mostrar o exame"
                                                    onDragStart={(e) => e.dataTransfer.setData('text/plain', JSON.stringify(serie))}><FaHandPaper /></span>
                                                <button
                                                    title="Gerar relatório"
                                                    onClick={() => handleDownloadRelatorio(serie.seriesID)}
                                                    className="btn-relatorio"
                                                >
                                                    <TbReportAnalytics style={{ fontSize: '15px' }} /> {/* Ícone ajustado */}
                                                </button>

                                            </div>
                                        </div>

                                    </div>

                                )}

                            </div>)
                    }
                </div>
                    <div className={`${styles.algorithmArea} m-2 py-2`}>
                    <h5>Escolha o Algoritmo</h5>
                    <div>
                        <select
                            name="cars"
                            id="cars"
                            onChange={(e) => {
                                // Converter o valor para número antes de atualizar o estado
                                setAlgoritmo(Number(e.target.value));
                            }}
                        >
                            <option value={1}>Segmentar pulmão</option>
                            <option value={2}>Classificação</option>
                            <option value={3}>[opção futura]</option>
                            <option value={4}>[opção futura]</option>
                        </select>
                        <button onClick={() => setExecutarSegmentacao(true)}>Ok</button>
                    </div>
                </div>
                <div className={styles.janelamentoDiv}>
                    <h5>Janelamento</h5>
                    <div>
                        <button id="plm">Pulmão</button>
                        <button id="osso">Osso</button>
                        <button id="torax">Tórax</button>
                    </div>
                </div>

                <div className={`${styles.aerationCard} m-2 py-2`}>
                    <h5>Níveis de Aeração</h5>
                    <div className={`${styles.aerationOptions}`}>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="hiper-aerada" />
                            <label htmlFor="hiper-aerada">Hiper Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'green' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="normalmente-aerada" />
                            <label htmlFor="normalmente-aerada">Normalmente Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'blue' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="pouco-aerada" />
                            <label htmlFor="pouco-aerada">Pouco Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'yellow' }}></div>
                        </div>
                        <div className={`${styles.aerationOption}`}>
                            <input type="checkbox" id="nada-aerada" />
                            <label htmlFor="nada-aerada">Nada Aerada</label>
                            <div className={styles.colorBox} style={{ backgroundColor: 'pink' }}></div>
                        </div>
                        <div className={`${styles.aerationOption} ${styles.customAeration}`}>
                            <input
                                type="checkbox"
                                id="custom-aeration"
                                checked={aerationCustom.enabled}
                                onChange={handleCustomToggle}
                            />
                            <label htmlFor="custom-aeration">Personalizada</label>

                            <div className={styles.customInputs}>
                                <div className={styles.huGroup}>
                                    <input
                                        type="number"
                                        placeholder="HU Min"
                                        value={aerationCustom.min}
                                        onChange={(e) => handleCustomChange("min", e.target.value)}
                                        className={styles.customInput}
                                    />
                                    <input
                                        type="number"
                                        placeholder="HU Max"
                                        value={aerationCustom.max}
                                        onChange={(e) => handleCustomChange("max", e.target.value)}
                                        className={styles.customInput}
                                    />
                                    <input
                                        type="color"
                                        value={aerationCustom.color}
                                        onChange={(e) => handleCustomChange("color", e.target.value)}
                                        className={styles.customColorInput}
                                    />
                                    <div
                                        className={styles.colorBox}
                                        style={{ backgroundColor: aerationCustom.color }}
                                    ></div>
                                </div>

                                <select
                                    onChange={handleHUSelection}
                                    defaultValue=""
                                    className={styles.huSelect}
                                >
                                    <option value="" disabled>
                                        Autocomplete
                                    </option>
                                    <option value="contrast">Contraste (100 a 600)</option>
                                    <option value="calcifications">Calcificações (30 a 500)</option>
                                    <option value="muscle">Músculo , tecido mole (20 a 40)</option>
                                    <option value="water">Água (0)</option>
                                    <option value="fat">Gordura (-70 a -30)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div className={styles.manualDiv} style={{
                    padding: '15px',
                    textAlign: 'center',
                    margin: '10px 0'
                }}>
                    <button
                        onClick={handleDownloadManual}
                        style={{
                            backgroundColor: 'hsl(133, 100%, 30%)', // Mesma cor verde dos outros botões
                            color: 'white',
                            border: 'none',
                            padding: '10px 15px',
                            borderRadius: '5px',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px'
                        }}
                    >
                        <span style={{ fontSize: '18px' }}></span> Manual do Usuário
                    </button>
                </div>
            </div>
        </nav>
    );
}

export default SideBar