import "./Password-change.css";
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
import { useState, useRef, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import axios from '../../api/axios';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaInfoCircle, FaCheck, FaTimes } from "react-icons/fa";
import { AuthContext } from '../../context/AuthProvider';

// Regex para validação de senha
const PWD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%-+]).{8,24}$/;

function PasswordChange() {
    const navigate = useNavigate();
    const { authData } = useContext(AuthContext);

    const [oldPassword, setOldPassword] = useState('');

    const [newPassword, setNewPassword] = useState('');
    const [validNewPassword, setValidNewPassword] = useState(false);
    const [newPasswordFocus, setNewPasswordFocus] = useState(false);

    const [confirmPassword, setConfirmPassword] = useState('');
    const [validConfirm, setValidConfirm] = useState(false);

    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState({ text: "", type: "" });

    const oldPasswordRef = useRef(null);

    // URL para alterar a senha do usuário logado
    const passwordChangeUrl = '/api/v1/password-change/';

    useEffect(() => {
        // Verificar se o usuário está autenticado
        if (!authData?.access) {
            navigate('/');
            return;
        }

        oldPasswordRef.current.focus();
    }, [authData, navigate]);

    // Validar senha
    useEffect(() => {
        const result = PWD_REGEX.test(newPassword);
        setValidNewPassword(result);

        // Validar confirmação de senha
        const match = newPassword === confirmPassword;
        setValidConfirm(match);
    }, [newPassword, confirmPassword]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setMessage({ text: "", type: "" });

        // Verificar se a senha é válida
        if (!validNewPassword || !validConfirm) {
            setMessage({
                text: "Por favor, verifique se a nova senha atende aos requisitos e se as senhas coincidem.",
                type: "danger"
            });
            setLoading(false);
            return;
        }

        try {
            const config = {
                headers: {
                    'Authorization': `Bearer ${authData.access}`,
                    'Content-Type': 'application/json',
                },
            };

            // Enviar solicitação para alterar a senha
            await axios.post(passwordChangeUrl, {
                old_password: oldPassword,
                new_password: newPassword,
                new_password2: confirmPassword
            }, config);

            setMessage({
                text: "Senha alterada com sucesso!",
                type: "success"
            });

            // Limpar os campos
            setOldPassword('');
            setNewPassword('');
            setConfirmPassword('');

        } catch (err) {
            console.error("Erro ao alterar senha:", err);
            let errorMessage = "Erro ao alterar senha. Verifique se a senha atual está correta.";

            // Verificar se há mensagens de erro específicas na resposta
            if (err.response && err.response.data) {
                if (err.response.data.old_password) {
                    errorMessage = `Erro: ${err.response.data.old_password.join(', ')}`;
                } else if (err.response.data.new_password) {
                    errorMessage = `Erro na nova senha: ${err.response.data.new_password.join(', ')}`;
                } else if (err.response.data.detail) {
                    errorMessage = err.response.data.detail;
                }
            }

            setMessage({
                text: errorMessage,
                type: "danger"
            });
        } finally {
            setLoading(false);
        }
    }

    return (
        <main className="passwordChange">
            <Form className={`p-5 rounded passwordChangeForm`} onSubmit={handleSubmit}>
                <h2>Alterar Senha</h2>
                <p>Preencha os campos abaixo para alterar sua senha.</p>

                {message.text && (
                    <Alert variant={message.type} className="mb-3">
                        {message.text}
                    </Alert>
                )}

                <Form.Group className="mb-3" controlId="formGroupOldPassword">
                    <Form.Label>Senha Atual</Form.Label>
                    <Form.Control
                        ref={oldPasswordRef}
                        type="password"
                        placeholder="Digite sua senha atual"
                        onChange={(e) => setOldPassword(e.target.value)}
                        value={oldPassword}
                        required
                    />
                </Form.Group>

                <Form.Group className="mb-3" controlId="formGroupNewPassword">
                    <Form.Label>
                        Nova Senha
                        {validNewPassword ? (
                            <FaCheck className="text-success ms-2" />
                        ) : (
                            <FaTimes className="text-danger ms-2" />
                        )}
                    </Form.Label>
                    <Form.Control
                        type="password"
                        placeholder="Digite sua nova senha"
                        onChange={(e) => setNewPassword(e.target.value)}
                        value={newPassword}
                        onFocus={() => setNewPasswordFocus(true)}
                        onBlur={() => setNewPasswordFocus(false)}
                        required
                    />
                    <div className={newPasswordFocus && !validNewPassword ? "passwordRequirements show" : "passwordRequirements hide"}>
                        <FaInfoCircle /> A senha deve ter:
                        <ul>
                            <li>8 a 24 caracteres</li>
                            <li>Pelo menos uma letra maiúscula</li>
                            <li>Pelo menos uma letra minúscula</li>
                            <li>Pelo menos um número</li>
                            <li>Pelo menos um caractere especial (!@#$%-+)</li>
                        </ul>
                    </div>
                </Form.Group>

                <Form.Group className="mb-4" controlId="formGroupConfirmPassword">
                    <Form.Label>
                        Confirmar Nova Senha
                        {validConfirm && validNewPassword ? (
                            <FaCheck className="text-success ms-2" />
                        ) : (
                            <FaTimes className="text-danger ms-2" />
                        )}
                    </Form.Label>
                    <Form.Control
                        type="password"
                        placeholder="Confirme sua nova senha"
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        value={confirmPassword}
                        required
                    />
                </Form.Group>

                <Button
                    variant=""
                    type="submit"
                    id="buttonCtm"
                    disabled={loading || !validNewPassword || !validConfirm}
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Processando...
                        </>
                    ) : (
                        "Alterar Senha"
                    )}
                </Button>
            </Form>
        </main>
    );
}

export default PasswordChange;
